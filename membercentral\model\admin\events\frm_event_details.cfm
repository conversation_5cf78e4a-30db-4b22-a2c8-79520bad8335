<cfsavecontent variable="local.detailsJS">
	<cfoutput>
	<script type="text/javascript" src="/assets/common/javascript/dhtmlxgrid/ext/dhtmlxgrid_drag.js"></script>
	<cfif local.strCrossEventFields.hasFields>
		<script type="text/javascript" src="/assets/common/javascript/resourceFields.js"></script>
		#local.strCrossEventFields.head#
	</cfif>
	<script language="javascript">
	<cfif arguments.event.getValue('mca_ta') eq "editEvent">
		var #ToScript(local.generateAndDownloadQRCodeLink,"link_generateAndDownloadQRCode")#
		function downloadQRCode(el) {
			el.removeClass('fal fa-qrcode').addClass('fa-solid fa-spinner fa-spin');
			$('##generateQRCode').load(link_generateAndDownloadQRCode);
		}
	</cfif>
	function checkEventDetailsForm() {
		$('button[name="btnSaveEventDetails"]').prop('disabled',true);

		var arrReq = [];
		if($('##eventContentTitle').val() == '') arrReq[arrReq.length] = "Enter a title for this event.";
		<cfif arguments.event.getValue('eID') gt 0>
			if($('##reportCode').val() == '') arrReq[arrReq.length] = "Enter an event code for this event.";
		</cfif>
		
		if ($('##categoryID').val() == '') arrReq[arrReq.length] = "Select a category for this event.";

		<cfif local.showRecurringEventSettings>
			if ($('##recurrenceAFID').val() > 0 && ! $('##recurrenceEndsOn').val()) arrReq.push('Enter a valid Recurrence End Date.');
		</cfif>

		<cfif arguments.event.getValue('mca_ta') eq "editEvent">
			var urlRegEx = new RegExp("#application.regEx.url#", "i");
			var remarketingURL = $('##remarketingURL').val().trim();
			if(remarketingURL.length > 0 && !urlRegEx.test(remarketingURL)) arrReq.push('Enter a valid Past Event Re-marketing Link.');
		</cfif>

		var errorMsgArray = [];
		<cfif local.strCrossEventFields.hasFields>
			#local.strCrossEventFields.jsValidation#
		</cfif>
		
		var redirectName = $.trim($('##newRedirectName').val());
		if(validateRedirectName(redirectName).length) arrReq.push(validateRedirectName(redirectName));

		/*drop empty elements*/
		var finalErrors = $.map(errorMsgArray, function(thisError){
			if (thisError.length) return thisError;
			else return null;
		});

		arrReq = arrReq.concat(finalErrors);

		if (arrReq.length) {
			$('button[name="btnSaveEventDetails"]').prop('disabled',false);
			mca_showAlert('err_eventdetails', arrReq.join('<br/>'), true);
			return false;
		} else {
			mca_hideAlert('err_eventdetails');
		}
		
		<cfif arguments.event.getValue('eID') gt 0>
			var chkEventCodeResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {debugger;
					if (typeof confirmRecurringEventChanges == 'function') confirmRecurringEventChanges('evdetail');
					else document.forms["frmEvent"].submit();
				} else {
					$('button[name="btnSaveEventDetails"]').prop('disabled',false);
					mca_showAlert('err_eventdetails', 'Event Code must be unique.', true);
				}
			};
			var objParams = { eventID:#arguments.event.getValue('eID')#, reportCode:$('##reportCode').val() };
			TS_AJX('ADMINEVENT','checkEventCode',objParams,chkEventCodeResult,chkEventCodeResult,10000,chkEventCodeResult);

			return false;
		<cfelse>
			return true;
		</cfif>
	}

	function closeBox() { MCModalUtils.hideModal(); }
		
	function initEventTimeSettings() {
		<cfif arguments.event.getValue('eID') GT 0 AND local.isRecurringEvent>
			<!--- prevent direct event time settings change --->
			$('##eventStartTime,##eventEndTime').prop('readonly',true);
			$('##eventTimeZoneID').prop('disabled',true);
			<cfif val(arguments.event.getValue('isAllDayEvent',0))>
				$('##eventStartTime').val(moment($('##eventStartTime').val().replace(' - ',' ')).format('M/D/YYYY'));
				$('##eventEndTime').val(moment($('##eventEndTime').val().replace(' - ',' ')).format('M/D/YYYY'));
			</cfif>
			return false;
		</cfif>

		if ($('##isAllDayEvent').is(':checked')) {
			$('##lockTimeZone').prop('disabled',true);
			$('##divEventTimeZone').hide();
			
			var startDate = $('##eventStartTime').val().length ? moment($('##eventStartTime').val().replace(' - ',' ')).format('M/D/YYYY') : '';
			var endDate = $('##eventEndTime').val().length ? moment($('##eventEndTime').val().replace(' - ',' ')).format('M/D/YYYY') : '';
			$('##eventStartTime').val(startDate);
			$('##eventEndTime').val(endDate);

			mca_setupDatePickerRangeFields('eventStartTime','eventEndTime');
		} else {
			$('##lockTimeZone').attr('disabled',false);
			$('##divEventTimeZone').show();

			var startDateTime = $('##eventStartTime').val().length ? moment($('##eventStartTime').val().replace(' - ',' ')).format('M/D/YYYY') + ' - #timeFormat(arguments.event.getValue('eventStartTime'),'h:mm TT')#' : '';
			var endDateTime = $('##eventEndTime').val().length ? moment($('##eventEndTime').val().replace(' - ',' ')).format('M/D/YYYY') + ' - #timeFormat(arguments.event.getValue('eventEndTime'),'h:mm TT')#' : '';
			$('##eventStartTime').val(startDateTime);
			$('##eventEndTime').val(endDateTime);
			$('##eventStartTime,##eventEndTime').prop('readonly',false);

			mca_setupDateTimePickerRangeFields('eventStartTime','eventEndTime',30);
		}

		<cfif local.showRecurringEventSettings>
			$('##eventStartTime').off('blur').on('blur', function() {
				let dtFormat = $('##isAllDayEvent').is(':checked') ? 'M/D/YYYY' : 'M/D/YYYY - h:mm A';
				let eventStartTime = $('##eventStartTime').val();
				if (eventStartTime.length) {
					mca_setupDatePickerField('recurrenceEndsOn', 
						moment(eventStartTime,dtFormat).clone().format('M/D/YYYY'), 
						moment(eventStartTime,dtFormat).clone().add(2,'y').format('M/D/YYYY'));
				}
			});
			$('##eventStartTime,##recurrenceEndsOn').blur();
		</cfif>
	}
	<cfif arguments.event.getValue('eID') EQ 0 AND local.showRecurringEventSettings>
		function onRecurrenceAFChange() {
			if ($('##recurrenceAFID').val() > 0) {
				$('##eventRegOptNone').click();
				$('.evRegOptions').addClass('d-none');
			} else {
				$('.evRegOptions').removeClass('d-none');
			}
		}
	</cfif>
	<cfif arguments.event.getValue('eID') GT 0 AND local.isRecurringEvent AND NOT local.inImportEventsQueue>
		function changeRecurringEventTime() {
			MCModalUtils.showModal({
				isslideout: true,
				size: 'lg',
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				title: 'Change Event Time',
				iframe: true,
				contenturl: '#local.changeRecurringEventTimeLink#'
			});
		}
		<cfif local.qryRecurringEvents.recordCount>
			function confirmRecurringEventChanges(mode) {
				$('button[name="btnSaveEventDetails"]').prop('disabled',false);
				
				MCModalUtils.showModal({
					verticallycentered: true,
					size: 'md',
					title: 'Edit Recurring Event',
					strmodalbody: {
						content: $('##mc_confirmRecurringEventChanges').html(),
					},
					strmodalfooter : {
						classlist: 'text-right',
						showclose: false,
						showextrabutton: true,
						extrabuttonclass: 'btn-primary',
						extrabuttonlabel: 'Save',
					}
				});

				$('##btnMCModalSave').on('click', function(){
					$(this).prop('disabled',true).html('Please wait <i class="fa-solid fa-spinner fa-spin"></i>');
					doConfirmRecurringEventChanges(mode, $('input[name="confirmOpt"]:checked').val() == 'all' ? 1 : 0);
				});
			}
			function doConfirmRecurringEventChanges(mode, x) {
				switch (mode) {
					case "evdetail":
						let updateUpcomingRecurringEvents = $('##updateUpcomingRecurringEvents');
						if (updateUpcomingRecurringEvents.length) {
							updateUpcomingRecurringEvents.val(x);
						} else {
							$('<input>').attr({ type: 'hidden', name: 'updateUpcomingRecurringEvents' }).val(x).appendTo('form##frmEvent');
						}
						document.forms["frmEvent"].submit();
						break;
					case "sponsors":
						if (x) doSaveSponsorInfoForUpcomingRecurringEvents();
						else MCModalUtils.hideModal();
						break;
				}
			}
			function doSaveSponsorInfoForUpcomingRecurringEvents() {
				var saveResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true') {
						MCModalUtils.hideModal();
					} else {
						$('##btnMCModalSave').prop('disabled',false).html('Save');
						alert('Unable to save sponsor details to the upcoming recurring events. Try again.');
					}
				};
				var objParams = { eventID:#arguments.event.getValue('eID')#, eventSRID:#local.strEvent.qryEventMeta.siteResourceID# };
				TS_AJX('ADMINEVENT','saveSponsorInfoForUpcomingRecurringEvents',objParams,saveResult,saveResult,40000,saveResult);
			}
		</cfif>
	</cfif>
	function previewConfirmationEmail(eid) {
		MCModalUtils.showModal({
			isslideout: true,
			size: 'lg',
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			title: 'Preview Confirmation Email',
			iframe: true,
			contenturl: '#this.link.previewConfirmationEmail#&cid=#arguments.event.getValue('cid')#&eid=' + eid
		});
	}
	function launchChangeCalendar(eid,cid) {
		MCModalUtils.showModal({
			isslideout: true,
			size: 'lg',
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			title: 'Move Event to a Different Calendar',
			iframe: true,
			contenturl: '#this.link.changeCalendar#&eid=' + eid + '&cid=' + cid,
			strmodalfooter : {
				classlist: 'd-flex',
				showclose: true,
				showextrabutton: true,
				extrabuttonclass: 'btn-primary ml-auto',
				extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##frmCalendar :submit").click',
				extrabuttonlabel: 'Save Changes',
			}
		});
	}

	function loadSelectedCategories(){
		var arrCategories = [];
		<cfloop query="local.strEvent.qryEventCategories">
			arrCategories.push({
				categoryid:#local.strEvent.qryEventCategories.categoryID#,
				categoryname:'#EncodeForJavaScript(local.strEvent.qryEventCategories.category)#',
				adminonly:#local.strEvent.qryEventCategories.visibility eq "A" ? 1 : 0#
			});
		</cfloop>
		var templateSource = $('##mc_evCategoryRow').html();
		var template = Handlebars.compile(templateSource);
		$('##tbodyEVC_holder').append(template({arrcategories:arrCategories}));
		resetCatIcons();
	}
	function addCategoryToEvent() {
		var evCategoryID = $('##addcategoryID').val();
		if (evCategoryID > 0) {
			var arrCategories = [{
				categoryid:evCategoryID,
				categoryname:$('##addcategoryID option[value='+evCategoryID+']').data('categoryname'),
				adminonly:$('##addcategoryID option[value='+evCategoryID+']').data('adminonly')
			}];
			var templateSource = $('##mc_evCategoryRow').html();
			var template = Handlebars.compile(templateSource);
			$('##tbodyEVC_holder').append(template({arrcategories:arrCategories}));
			populateCatField();
			resetCatIcons();
			populateCategoryDropdown();
		}
	}
	function removeCategoryFromEvent(cid) {
		$('div ##trEVC_'+cid).remove();
		populateCatField();
		resetCatIcons();
		populateCategoryDropdown();
	}
	function moveCategoryUpInEvent(el) {
		var tr = el.closest('tr');
		var trprev = tr.prev();
		tr.remove().insertBefore(trprev);
		populateCatField();
		resetCatIcons();
	}
	function moveCategoryDownInEvent(el) {
		var tr = el.closest('tr');
		var trnext = tr.next();
		tr.remove().insertAfter(trnext);
		populateCatField();
		resetCatIcons();
	}
	function populateCategoryDropdown() {
		var inEventCatIDarr = $('##categoryID').val().split(',');
		var arrCats = [];
		<cfloop query="local.qryCategories">
			if (inEventCatIDarr.indexOf('#local.qryCategories.categoryID#') == -1) {
				arrCats.push({ text:'#JSStringFormat(local.qryCategories.category)#', value:#local.qryCategories.categoryID#, adminonly:#local.qryCategories.visibility eq "A" ? 1 : 0# });
			}
		</cfloop>
		arrCats.sort(function(a,b) {
			return a.text.localeCompare(b.text);
		});
		var tmpOps = '<option value="0">Add category to event</option>';
		$.each(arrCats, function(i) {
			tmpOps += '<option value="' + arrCats[i].value + '" data-categoryname="'+arrCats[i].text+'" data-adminonly="' + arrCats[i].adminonly + '">' + arrCats[i].text + (arrCats[i].adminonly == 1 ? ' (Admin Only)' : '') + '</option>';
		});
		$('##addcategoryID').find('option').remove().end().append(tmpOps);
	}
	function populateCatField() {
		var tmpVal = [];
		$('.trEVC:visible').each(function() { 
			tmpVal.push($(this).attr('mccatid'));
		});
		$('##categoryID').val(tmpVal.join(','));
	}
	function resetCatIcons() {
		$('.trEVC .fa-circle-arrow-up, .trEVC .fa-circle-arrow-down, .trEVC .fa-circle-minus').hide();
		var count = $('.trEVC:visible').length;
		if (count > 1) {
			$('.trEVC:visible').find('i.fa-circle-arrow-up').show();
			$('.trEVC:visible').find('i.fa-circle-arrow-down').show();
			$('.trEVC:visible').find('i.fa-circle-minus').show();
			$('.trEVC:visible:first').find('i.fa-circle-arrow-up').hide();
			$('.trEVC:visible:first').find('i.fa-circle-minus').hide();
			$('.trEVC:visible:last').find('i.fa-circle-arrow-down').hide();
		}
	}

	<cfif arguments.event.getValue('eID') gt 0>
		function reloadEventFeaturedImageSection() {
			var reloadImageResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') $('##eventFeaturedImageContainer').html(r.ftdimagehtml);
				else alert('Some error occured while loading the featured image.');
			};
			var objParams = {
				siteResourceID:#this.siteResourceID#, calendarID:#local.qryCalendar.calendarID#, eventID:#local.strEvent.qryEventMeta.eventID#,
				title:'#EncodeForJavaScript(local.strEvent.qryEventMeta.eventContentTitle)#' };
			$('##eventFeaturedImageContainer').html('<h5>Event Featured Image</h5>' + mca_getLoadingHTML());
			TS_AJX('ADMINEVENT','getEventFeaturedImagesHTML',objParams,reloadImageResult,reloadImageResult,20000,reloadImageResult);
		}
	</cfif>

	function onBlurEventRedirectName(redirectName) {
		mca_hideAlert('err_eventdetails');
		
		if(validateRedirectName(redirectName).length) return;
		
		var checkQuickLinkResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true') {
				setEventQuickLinkTestMessage(r.isduplicate == false);
			} else {
				$('##redirectBox').addClass('d-none');
				mca_showAlert('err_eventdetails', 'We were unable to check whether this quick link exists.');
			}
		};
		
		if(redirectName.length) checkUniqueEventQuickLink(redirectName, checkQuickLinkResult);
		else $('##redirectBox').addClass('d-none');
	}
	function checkUniqueEventQuickLink(redirectName,callback) {
		var objParams = { redirectID:#val(local.strEvent.qryEventMeta.redirectID)#, redirectName:redirectName };
		TS_AJX('ALIAS','checkUniqueQuickLink', objParams, callback,callback,10000,callback);
	}
	function setEventQuickLinkTestMessage(success){
		$('##redirectImg').toggleClass('fa-circle-check', success).toggleClass('fa-circle-exclamation', !success);
		$('##redirectText').html(success ? 'OK!' : 'Quick Link already used!');
		$('##redirectBox').toggleClass('text-green', success).toggleClass('text-danger', !success).removeClass('d-none');
	}
	function validateRedirectName(redirectName) {
		var invalidRedirectNameMsg = "";
		if (redirectName.length && redirectName.startsWith('?')) {
			invalidRedirectNameMsg = "Enter a valid quick link. It cannot begin with a question mark.";
			var success = false;
			$('##redirectImg').toggleClass('fa-circle-check', success).toggleClass('fa-circle-exclamation', !success);
			$('##redirectText').html(invalidRedirectNameMsg);
			$('##redirectBox').toggleClass('text-green', success).toggleClass('text-danger', !success).removeClass('d-none');			
		}
		return invalidRedirectNameMsg;
	}

	$(function() {
		loadSelectedCategories();
		populateCategoryDropdown();
		initEventTimeSettings();
		mca_setupCalendarIcons('frmEvent');
		mca_setupSelect2();
	});
	</script>
	<cfif arguments.event.getValue('eID') gt 0 and val(local.qryCalendar.featureImageConfigID) gt 0>
		#local.strFeaturedImages.js#
	</cfif>
	<style type="text/css">
		p span { display: block; }
		.masterEvList { margin-top:0; margin-left:0; padding-left:0;}
		.masterEvList li { margin-top:0; margin-bottom:0; <cfif listLen(local.masterEventNameList,"|") lte 1>list-style:none;</cfif>}
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.detailsJS#">

<cfoutput>
<div id="err_eventdetails" class="alert alert-danger mb-2 d-none"></div>

<form name="frmEvent" id="frmEvent" class="mb-3" action="#this.link.saveEventDetails#" method="POST" onsubmit="return checkEventDetailsForm();">
<input type="hidden" name="cid" value="#arguments.event.getValue('cid')#">
<input type="hidden" name="eID" value="#arguments.event.getValue('eID')#">
<input type="hidden" name="eventTypeID" value="#arguments.event.getValue('eventTypeID')#">
<input type="hidden" name="eventContentID" value="#arguments.event.getValue('eventContentID')#">
<input type="hidden" name="contactContentID" value="#arguments.event.getValue('contactContentID')#">
<input type="hidden" name="locationContentID" value="#arguments.event.getValue('locationContentID')#">
<input type="hidden" name="travelContentID" value="#arguments.event.getValue('travelContentID')#">
<input type="hidden" name="cancelContentID" value="#arguments.event.getValue('cancelContentID')#">
<input type="hidden" name="informationContentID" value="#arguments.event.getValue('informationContentID')#">
<input type="hidden" name="oldEventContentTitle" value="#replace(arguments.event.getValue('eventContentTitle'),chr(34),'&quot;','ALL')#">
<input type="hidden" name="oldEventStartTime" value="#dateFormat(arguments.event.getValue('eventStartTime'),'m/d/yyyy')# - #timeFormat(arguments.event.getValue('eventStartTime'),'h:mm tt')#">
<input type="hidden" name="oldReportCode" value="#replace(arguments.event.getValue('reportCode'),chr(34),'&quot;','ALL')#">
<input type="hidden" name="oldCategoryIDList" value="#arguments.event.getValue('categoryID')#">
<input type="hidden" name="redirectID" id="redirectID" value="#local.strEvent.qryEventMeta.redirectID#">
<input type="hidden" name="currentRedirectName" id="currentRedirectName" value="#local.strEvent.qryEventMeta.redirectName#">

<cfif arguments.event.getValue('eID') eq 0>
	<input type="hidden" name="status" value="#arguments.event.getValue('status')#" />
	<input type="hidden" name="lockTimeZone" value="#arguments.event.getValue('lockTimeZone',0)#" />
	<input type="hidden" name="hiddenFromCalendar" value="#arguments.event.getValue('hiddenFromCalendar',0)#" />
	<input type="hidden" name="contactContentTitle" id="contactContentTitle" value="" />
	<input type="hidden" name="contactContent" id="contactContent" value="" />
	<input type="hidden" name="locationContentTitle" id="locationContentTitle" value="" />
	<input type="hidden" name="locationContent" id="locationContent" value="" />
	<input type="hidden" name="cancelContentTitle" id="cancelContentTitle" value="" />
	<input type="hidden" name="cancelContent" id="cancelContent" value="" />
	<input type="hidden" name="travelContentTitle" id="travelContentTitle" value="" />
	<input type="hidden" name="travelContent" id="travelContent" value="" />
</cfif>

<cfif local.strEvent.qryEventRegMeta.registrationType neq "Reg">
	<input type="hidden" name="informationContentTitle" id="informationContentTitle" value="" />
	<input type="hidden" name="informationContent" id="informationContent" value="" />
</cfif>
<input type="hidden" name="peID" value="#arguments.event.getValue('peID',0)#">
<cfif val(arguments.event.getValue('peID',0))>
	<input type="hidden" name="allowSubEvents" value="0">
</cfif>

<div class="row">
	<div class="col text-right">
		<cfif arguments.event.getValue('eID') gt 0>
			<button type="button" name="btnShowPerms" class="btn btn-sm btn-secondary" onclick="mca_showPermissions(#this.siteResourceID#,'Event');">Permissions</button>
		</cfif>
		<button type="submit" name="btnSaveEventDetails" class="btn btn-sm btn-primary">Save Details</button>		
	</div>
</div>

<h5>Basic Event Information</h5>

<cfif arguments.event.getValue('eID') gt 0>
	<div class="form-group row">
		<label class="col-sm-2 col-form-label">Calendar</label>
		<div class="col-sm-10 col-form-label">
			#local.qryCalendar.calendarName#
			<button type="button" name="btnChangeCalendar" class="btn btn-sm btn-link" onclick="launchChangeCalendar(#arguments.event.getValue('eid')#, #arguments.event.getValue('cid')#);">Change Calendar</button>
		</div>
	</div>
</cfif>
<cfif val(arguments.event.getValue('peID',0)) or local.strEvent.isSubEvent>
	<div class="form-group row">
		<label class="col-sm-2 col-form-label">Parent Events</label>
		<div class="col-sm-10">
			<ul class="masterEvList">#replace(local.masterEventNameList,"|","","all" )#</ul>
		</div>
	</div>
</cfif>
<cfif arguments.event.getValue('eID') gt 0>
	<div class="form-group row">
		<label for="status" class="col-sm-2 col-form-label-sm font-size-md">Status *</label>
		<div class="col-sm-10">
			<div class="row">
				<div class="col-12 col-xl pr-xl-0">
					<select name="status" id="status" class="form-control form-control-sm">
						<option value="A" <cfif arguments.event.getValue('status') eq "A">selected</cfif>>Active</option>
						<option value="I" <cfif arguments.event.getValue('status') eq "I">selected</cfif>>Inactive</option>
					</select>
				</div>
				<div class="col-12 col-xl-auto pl-xl-1 d-flex align-items-center">Created by #local.createdByMember.firstname# #local.createdByMember.lastname# (#local.createdByMember.membernumber#)</div>
			</div>
		</div>
	</div>
</cfif>
<div class="form-group row mt-4">
	<label for="eventContentTitle" class="col-sm-2 col-form-label-sm font-size-md">Title of Event *</label>
	<div class="col-sm-10">
		<input type="text" name="eventContentTitle" id="eventContentTitle" class="form-control form-control-sm" value="#replace(arguments.event.getValue('eventContentTitle'),chr(34),'&quot;','ALL')#" maxlength="200"/>
	</div>
</div>
<div class="form-group row">
	<label for="eventSubTitle" class="col-sm-2 col-form-label-sm font-size-md">Sub Title of Event</label>
	<div class="col-sm-10">
		<input type="text" name="eventSubTitle" id="eventSubTitle" class="form-control form-control-sm" value="#replace(arguments.event.getValue('eventSubTitle'),chr(34),'&quot;','ALL')#" maxlength="200"/>
	</div>
</div>
<div class="form-group row mb-4">
	<label for="eventStartTime" class="col-sm-2 col-form-label-sm font-size-md">Time of Event *</label>
	<div class="col-sm-10">
		<div class="row no-gutters">
			<div class="col-md col-sm-12 pr-md-0">
				<div class="input-group input-group-sm">
					<input type="text" name="eventStartTime" id="eventStartTime" value="#dateFormat(arguments.event.getValue('eventStartTime'),'m/d/yyyy')# - #timeFormat(arguments.event.getValue('eventStartTime'),'h:mm tt')#" class="form-control form-control-sm dateControl" placeholder="From">
					<div class="input-group-append">
						<span class="input-group-text cursor-pointer calendar-button" data-target="eventStartTime"><i class="fa-solid fa-calendar"></i></span>
					</div>
				</div>
			</div>
			<div class="col-md-auto px-md-2 d-flex align-items-center">to</div>
			<div class="col-md col-sm-12 px-md-0">
				<div class="input-group input-group-sm">
					<input type="text" name="eventEndTime" id="eventEndTime" value="#dateFormat(arguments.event.getValue('eventEndTime'),'m/d/yyyy')# - #timeFormat(arguments.event.getValue('eventendTime'),'h:mm tt')#" class="form-control form-control-sm dateControl" placeholder="To">
					<div class="input-group-append">
						<span class="input-group-text cursor-pointer calendar-button" data-target="eventEndTime"><i class="fa-solid fa-calendar"></i></span>
					</div>
				</div>
			</div>
			<div class="col-md-auto pl-md-2" id="divEventTimeZone" <cfif val(arguments.event.getValue('isAllDayEvent',0))>style="display:none;"</cfif>>
				<select id="eventTimeZoneID" name="eventTimeZoneID" class="form-control form-control-sm">
					<cfloop query="local.qryTimeZones">
						<option value="#local.qryTimeZones.timeZoneID#" <cfif (val(arguments.event.getValue('lockTimeZone',0)) AND (arguments.event.getValue('lockTimeZone',0) eq local.qryTimeZones.timeZoneID)) OR (not val(arguments.event.getValue('lockTimeZone',0)) AND arguments.event.getValue('mc_siteinfo.defaultTimeZoneID') eq local.qryTimeZones.timeZoneID)>selected</cfif>>#local.qryTimeZones.timeZone#</option>
					</cfloop>
				</select>
			</div>
		</div>
		<cfif arguments.event.getValue('eID') GT 0 AND local.isRecurringEvent>
			<cfif local.inImportEventsQueue>
				<div class="my-2"><a href="##" class="font-weight-bold font-size-sm text-dim" onclick="return false;"><i class="fa-solid fa-calendar-days"></i> Change event time</a></div>
			<cfelse>
				<div class="my-2"><a href="##" class="font-weight-bold font-size-sm" onclick="changeRecurringEventTime();"><i class="fa-solid fa-calendar-days"></i> Change event time</a></div>
			</cfif>
			<div class="mt-1">
				<cfif val(arguments.event.getValue('isAllDayEvent',0))><i class="fa-light fa-square-check text-success"></i><cfelse><i class="fa-light fa-square text-dim"></i></cfif> This is an all-day event
			</div>
			<div class="mt-1">
				<cfif val(arguments.event.getValue('lockTimeZone',0))><i class="fa-light fa-square-check text-success"></i><cfelse><i class="fa-light fa-square text-dim"></i></cfif> Lock this event so it always displays in the selected time zone.
			</div>
		<cfelse>
			<div class="row mt-1">
				<div class="col">
					<div class="form-check">
						<input type="checkBox" id="isAllDayEvent" name="isAllDayEvent" value="1" class="form-check-input" onClick="initEventTimeSettings();"<cfif val(arguments.event.getValue('isAllDayEvent',0))> checked</cfif>>
						<label class="form-check-label" for="isAllDayEvent">This is an all-day event</label>
					</div>
					<cfif arguments.event.getValue('eID') gt 0>
						<div class="form-check">
							<input type="checkBox" id="lockTimeZone" name="lockTimeZone" value="1" class="form-check-input"<cfif val(arguments.event.getValue('isAllDayEvent',0))>disabled</cfif> <cfif val(arguments.event.getValue('lockTimeZone',0))>checked</cfif>>
							<label class="form-check-label" for="lockTimeZone">Lock this event so it always displays in the selected time zone.</label>
						</div>
					</cfif>
				</div>
			</div>
		</cfif>
	</div>
</div>
<cfif local.showRecurringEventSettings>
	<div class="form-group row mb-4">
		<label for="recurrenceAFID" class="col-sm-2 col-form-label-sm font-size-md">Event Recurrence</label>
		<div class="col-sm-10">
			<div class="d-flex">
				<div class="col px-0">
					<div class="form-label-group">
						<select id="recurrenceAFID" name="recurrenceAFID" class="custom-select"<cfif arguments.event.getValue('eID') EQ 0> onchange="onRecurrenceAFChange();"</cfif>>
							<option value=""></option>
							<cfloop query="local.qrySiteAFs">
								<option value="#local.qrySiteAFs.AFID#">#local.qrySiteAFs.afName#</option>
							</cfloop>
						</select>
						<label for="recurrenceAFID">Advance Formula</label>
					</div>
				</div>
				<div class="col pl-2 pr-0">
					<div class="form-label-group">
						<div class="input-group dateFieldHolder">
							<input type="text" name="recurrenceEndsOn" id="recurrenceEndsOn" value="" class="form-control dateControl" autocomplete="off">
							<div class="input-group-append">
								<span class="input-group-text cursor-pointer calendar-button" data-target="recurrenceEndsOn"><i class="fas fa-calendar"></i></span>
								<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('recurrenceEndsOn');"><i class="fas fa-times-circle"></i></a></span>
							</div>
							<label for="recurrenceEndsOn">Ends</label>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</cfif>
<cfif arguments.event.getValue('eID') gt 0>
	<cfif local.isRecurringEvent>
		<cfif local.inImportEventsQueue>
			<div class="form-group row mb-4">
				<label class="col-sm-2 col-form-label-sm font-size-md">Event Recurrence</label>
				<div class="col">
					<div class="alert alert-info mb-0"><i class="fa-solid fa-circle-info"></i> Future events in this series are in the queue and will be updated shortly.</div>
				</div>
			</div>
		</cfif>
		<cfif local.qryRecurringEvents.recordCount>
			<div class="form-group row mb-4">
				<label for="nextEventStartTime" class="col-sm-2 col-form-label-sm font-size-md">Next Events in this Recurring Series</label>
				<div class="col-sm-10 pl-2">
					<div class="timeline-list">
						<cfloop query="local.qryRecurringEvents">
							<div class="timeline-item">
								<div class="timeline-item--content">
									<div class="timeline-item--icon"></div>
									<h4 class="timeline-item--label">
										<a href="#this.link.editEvent#&eID=#local.qryRecurringEvents.eventID#" target="mcev#local.qryRecurringEvents.eventID#" class="font-size-md">
											#local.qryRecurringEvents.eventTitle#
										</a>
									</h4>
									<div class="mt-1">
										<i class="far fa-clock mr-1"></i>
										#local.objEvent.generateEventDateString(mode='eventConfirmation', startTime=local.qryRecurringEvents.startTime, endTime=local.qryRecurringEvents.endTime, 
											isAllDayEvent=local.qryRecurringEvents.isAllDayEvent, showTimeZone=true, timeZoneAbbr=local.qryRecurringEvents.timezone)#
									</div>
								</div>
							</div>
						</cfloop>
					</div>
				</div>
			</div>
		</cfif>
	</cfif>
	
	<div class="form-group row">
		<label for="reportCode" class="col-sm-2 col-form-label-sm font-size-md">Event Code *</label>
		<div class="col-sm-10">
			<div class="row">
				<div class="col-12 col-xl pr-xl-0">
					<input type="text" id="reportCode" name="reportCode" value="#replace(arguments.event.getValue('reportCode'),chr(34),'&quot;','ALL')#" class="form-control form-control-sm" maxlength="15" autocomplete="off">
				</div>
				<div class="col-12 col-xl-auto pl-xl-1 d-flex align-items-center">(unique code to identify this event in reports and imports)</div>
			</div>
		</div>
	</div>
	<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<div class="form-group row">
			<label for="evUID" class="col-sm-2 col-form-label-sm font-size-md">API ID *</label>
			<div class="col-sm-10">
				<input type="text" name="evUID" id="evUID" value="#arguments.event.getValue('eventUID')#" class="form-control form-control-sm" autocomplete="off" maxlength="60">
			</div>
		</div>
	<cfelse>
		<div class="form-group row">
			<label class="col-sm-2 col-form-label">API ID</label>
			<div class="col-sm-10 col-form-label">#arguments.event.getValue('eventUID')#</div>
		</div>
	</cfif>
	<div class="form-group row">
		<label class="col-sm-2 col-form-label-sm font-size-md">Hidden</label>
		<div class="col-sm-10">
			<div class="form-check">
				<input type="checkBox" id="hiddenFromCalendar" name="hiddenFromCalendar" value="1" class="form-check-input" <cfif val(arguments.event.getValue('hiddenFromCalendar',0))>checked</cfif>>
				<label class="form-check-label" for="hiddenFromCalendar">Hide this event from the calendar</label>
			</div>
		</div>
	</div>
</cfif>

<div class="form-group row mt-4">
	<label class="col-sm-2 col-form-label">Categories *</label>
	<div class="col-sm-10">
		<div class="row">
			<div class="col">
				<input type="hidden" name="categoryID" id="categoryID" value="#arguments.event.getValue('categoryID')#">
				<table class="table table-sm table-borderless">
				<thead>
					<tr>
						<th colspan="4">Selected Categories</th>
					</tr>
				</thead>
				<tbody id="tbodyEVC_holder"></tbody>
				</table>
			</div>
		</div>
		<div class="row">
			<div class="col">
				<select name="addcategoryID" id="addcategoryID" class="form-control form-control-sm" onChange="addCategoryToEvent();"></select>
			</div>
		</div>
	</div>
</div>

<div class="form-group mt-5">
	#application.objWebEditor.showContentBoxWithLinks(fieldname='eventContent', fieldlabel='Event Description', contentID=arguments.event.getValue('eventContentID'), content=arguments.event.getValue('eventContent'), allowMergeCodes=0, supportsBootstrap=true, allowVersioning=true)#
</div>

<cfif arguments.event.getValue('eID') gt 0 and local.qryAssetCategories.recordCount>
	<cfinclude template="frm_eventAssetDetails.cfm">
</cfif>

<cfif local.strCrossEventFields.hasFields>
	<h5 class="mt-5">Event Custom Fields</h5>
	<div class="pl-3">
		#local.strCrossEventFields.html#
	</div>
</cfif>

<cfif arguments.event.getValue('eID') gt 0 and val(local.qryCalendar.featureImageConfigID) gt 0>
	<div class="mt-5" id="eventFeaturedImageContainer">
		#local.strFeaturedImages.html#
	</div>
</cfif>

<cfif arguments.event.getValue('eID') gt 0>
	<h5 class="mt-5">Additional Event Information</h5>

	<div class="form-group row mt-3">
		<label for="contactContentTitle" class="col-auto">Contact Title</label>
		<cfif arguments.event.getValue('registrationTypeID') GT 0>
			<div class="col text-right">
				<span>
					<label for="emailContactContent">Include Contact in confirmation e-mail?</label>
					<input type="checkbox" name="emailContactContent" id="emailContactContent" value="1" <cfif arguments.event.getValue('emailContactContent')>checked="checked"</cfif>>
				</span>
			</div>
		</cfif>
		<div class="col-12">
			<input type="text" name="contactContentTitle" id="contactContentTitle" value="#replace(arguments.event.getValue('contactContentTitle'),chr(34),'&quot;','ALL')#" class="form-control form-control-sm">
		</div>
	</div>
	<div class="form-group mt-2">
		#application.objWebEditor.showContentBoxWithLinks(fieldname='contactContent', fieldlabel='Contact Detail', contentID=arguments.event.getValue('contactContentID'), content=arguments.event.getValue('contactContent'), allowMergeCodes=0, supportsBootstrap=true, allowVersioning=true)#
	</div>

	<div class="form-group row mt-4">
		<label for="locationContentTitle" class="col-auto">Location Title</label>
		<cfif arguments.event.getValue('registrationTypeID') GT 0>
			<div class="col text-right">
				<span>
					<label for="emailLocationContent">Include Location in confirmation e-mail?</label>
					<input type="checkbox" name="emailLocationContent" id="emailLocationContent" value="1" <cfif arguments.event.getValue('emailLocationContent')>checked="checked"</cfif>>
				</span>
			</div>
		</cfif>
		<div class="col-12">
			<input type="text" name="locationContentTitle" id="locationContentTitle" value="#replace(arguments.event.getValue('locationContentTitle'),chr(34),'&quot;','ALL')#" class="form-control form-control-sm">
		</div>
	</div>
	<div class="form-group mt-2">
		#application.objWebEditor.showContentBoxWithLinks(fieldname='locationContent', fieldlabel='Location Detail', contentID=arguments.event.getValue('locationContentID'), content=arguments.event.getValue('locationContent'), allowMergeCodes=0, supportsBootstrap=true, allowVersioning=true)#
	</div>

	<div class="form-group row mt-4">
		<label for="cancelContentTitle" class="col-auto">Cancellation Title</label>
		<cfif arguments.event.getValue('registrationTypeID') GT 0>
			<div class="col text-right">
				<span>
					<label for="emailCancelContent">Include Cancellation in confirmation e-mail?</label>
					<input type="checkbox" name="emailCancelContent" id="emailCancelContent" value="1" <cfif arguments.event.getValue('emailCancelContent')>checked="checked"</cfif>>
				</span>
			</div>
		</cfif>
		<div class="col-12">
			<input type="text" name="cancelContentTitle" value="#replace(arguments.event.getValue('cancelContentTitle'),chr(34),'&quot;','ALL')#" class="form-control form-control-sm">
		</div>
	</div>
	<div class="form-group mt-2">
		#application.objWebEditor.showContentBoxWithLinks(fieldname='cancelContent', fieldlabel='Cancellation Policy', contentID=arguments.event.getValue('cancelContentID'), content=arguments.event.getValue('cancelContent'), allowMergeCodes=0, supportsBootstrap=true, allowVersioning=true)#
	</div>

	<div class="form-group row mt-4">
		<label for="travelContentTitle" class="col-auto">Travel Title</label>
		<cfif arguments.event.getValue('registrationTypeID') GT 0>
			<div class="col text-right">
				<span>
					<label for="emailTravelContent">Include Travel in confirmation e-mail?</label>
					<input type="checkbox" name="emailTravelContent" id="emailTravelContent" value="1" <cfif arguments.event.getValue('emailTravelContent')>checked="checked"</cfif>>
				</span>
			</div>
		</cfif>
		<div class="col-12">
			<input type="text" name="travelContentTitle" value="#replace(arguments.event.getValue('travelContentTitle'),chr(34),'&quot;','ALL')#" class="form-control form-control-sm">
		</div>
	</div>
	<div class="form-group mt-2">
		#application.objWebEditor.showContentBoxWithLinks(fieldname='travelContent', fieldlabel='Travel Detail', contentID=arguments.event.getValue('travelContentID'), content=arguments.event.getValue('travelContent'), allowMergeCodes=0, supportsBootstrap=true, allowVersioning=true)#
	</div>	
</cfif>

<cfif local.strEvent.qryEventRegMeta.registrationType eq "Reg">
	<div class="form-group row mt-4<cfif local.isRecurringEvent> d-none</cfif>">
		<label for="informationContentTitle" class="col-12">Information Title</label>
		<div class="col-12">
			<input type="text" name="informationContentTitle" value="#replace(arguments.event.getValue('informationContentTitle'),chr(34),'&quot;','ALL')#" class="form-control form-control-sm">
		</div>
	</div>
	<div class="form-group mt-2<cfif local.isRecurringEvent> d-none</cfif>">
		#application.objWebEditor.showContentBoxWithLinks(fieldname='informationContent', fieldlabel='Information to Include in Confirmation Email', contentID=arguments.event.getValue('informationContentID'), content=arguments.event.getValue('informationContent'), allowMergeCodes=1, mergeCodeList="eid=#arguments.event.getValue('eid')#&incEV", supportsBootstrap=true, allowVersioning=true)#
	</div>
</cfif>

<cfif arguments.event.getValue('mca_ta') eq "editEvent">
	<div class="form-group row mt-5">
		<label for="internalNotes" class="col-sm-3 col-form-label-sm font-size-md">Internal Notes about this event</label>
		<div class="col-sm-9">
			<textarea name="internalNotes" id="internalNotes" class="form-control form-control-sm" rows="5">#HTMLEditFormat(arguments.event.getValue('internalNotes'))#</textarea>
		</div>
	</div>
	<div class="form-row mt-3">
		<div class="col">
			<div class="form-group">
				<div class="form-label-group">
					<div class="input-group">
						<input type="text" name="justview1" id="justview1" value="/?#local.baseTestLink#&evAction=showDetail&eid=#arguments.event.getValue('eid')#" class="form-control" readonly="readonly" onclick="this.select();"/>
						<div class="input-group-append">
							<span class="input-group-text"><a target="_blank" href="/?#local.baseTestLink#&evAction=showDetail&eid=#arguments.event.getValue('eid')#"><i class="fa-solid fa-up-right-from-square"></i></a></span>
						</div>
						<label for="justview1">Internal Event URL</label>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="form-row">
		<div class="col">
			<div class="form-group">
				<div class="form-label-group">
					<div class="input-group">
						<input type="text" name="justview2" id="justview2" value="#arguments.event.getValue('mc_siteInfo.scheme','http')#://#arguments.event.getValue('mc_siteInfo.mainhostname')#/?#local.baseTestLink#&evAction=showDetail&eid=#arguments.event.getValue('eid')#" class="form-control" readonly="readonly" onclick="this.select();"/>
						<div class="input-group-append">
							<span class="input-group-text"><i class="fa-lg fal fa-qrcode" title="Generate QR Code" onclick="downloadQRCode($(this))"></i></span>
							<div id="generateQRCode" class="d-none"></div>
							<span class="input-group-text"><a target="_blank" href="/?#local.baseTestLink#&evAction=showDetail&eid=#arguments.event.getValue('eid')#"><i class="fa-solid fa-up-right-from-square"></i></a></span>
						</div>
						<label for="justview2">External Event URL</label>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="form-row mt-2">
		<div class="col">
			<div class="form-group">
				<div class="form-label-group mb-2">
					<div class="input-group input-group">
						<div class="input-group-prepend">
							<span class="input-group-text">#arguments.event.getValue('mc_siteInfo.scheme','http')#://#arguments.event.getValue('mc_siteInfo.mainhostname')#/</span>
						</div>
						<input type="text" name="newRedirectName" id="newRedirectName" value="#local.strEvent.qryEventMeta.redirectName#" class="form-control py-0" onblur="onBlurEventRedirectName(this.value);">
						<label for="newRedirectName">Quick Link</label>
					</div>
				</div>
			</div>
			<div id="redirectBox" class="d-none text-right pr-1">
				<i class="fa-solid" id="redirectImg"></i> <span id="redirectText"></span>
			</div>
		</div>
	</div>

	<h5 class="mt-5">Past Event Re-Marketing</h5>
	<div class="form-group row">
		<label for="remarketingURL" class="col-sm-3 col-form-label-sm font-size-md">Past Event Re-marketing URL</label>
		<div class="col-sm-9">
			<input type="text" name="remarketingURL" id="remarketingURL" value="#arguments.event.getValue('remarketingURL','')#" class="form-control form-control-sm">
		</div>
	</div>
</cfif>

<cfif arguments.event.getValue('eID') eq 0>
	<h5 class="mt-5 evRegOptions">Event Registration Options</h5>
	<div class="form-group row evRegOptions">
		<div class="col pl-md-5">
			<div class="form-check">
				<input type="radio" id="eventRegOptOnline" name="eventRegistrationOption" value="online" class="form-check-input" <cfif val(arguments.event.getValue('peID',0))>checked</cfif>>
				<label class="form-check-label" for="eventRegOptOnline">Enable full online registration for this event</label>
			</div>
			<div class="form-check">
				<input type="radio" id="eventRegOptRSVP" name="eventRegistrationOption" value="rsvp" class="form-check-input">
				<label class="form-check-label" for="eventRegOptRSVP">Enable RSVP for this event</label>
			</div>
			<div class="form-check">
				<input type="radio" id="eventRegOptURL" name="eventRegistrationOption" value="alturl" class="form-check-input">
				<label class="form-check-label" for="eventRegOptURL">Enable an alternate URL registration for this event</label>
			</div>
			<div class="form-check">
				<input type="radio" id="eventRegOptNone" name="eventRegistrationOption" value="none" class="form-check-input" <cfif val(arguments.event.getValue('peID',0)) eq 0>checked</cfif>>
				<label class="form-check-label" for="eventRegOptNone">Do not activate any registration</label>
			</div>
		</div>
	</div>
</cfif>

<div class="row mt-3">
	<div class="col text-right">
		<button type="submit" name="btnSaveEventDetails" class="btn btn-sm btn-primary">Save Details</button>
		<cfif val(arguments.event.getValue('eID'))>
			<button type="button" name="btnPreviewEmail" class="btn btn-sm btn-secondary" onclick="previewConfirmationEmail(#arguments.event.getValue('eid')#);">Preview E-mail</button>
		</cfif>
	</div>
</div>

</form>

<cfif arguments.event.getValue('mca_ta') eq "editEvent">
	#local.strSponsors.html#
</cfif>

<script id="mc_evCategoryRow" type="text/x-handlebars-template">
	{{##each arrcategories}}
		<tr id="trEVC_{{categoryid}}" class="trEVC" mccatid="{{categoryid}}">
			<td>{{categoryname}}{{##compare adminonly '==' 1}} <span class="badge badge-info ml-1">Admin Only</span>{{/compare}}</td>
			<td width="25"><i class="fa-solid fa-circle-arrow-up fa-lg cursor-pointer" onclick="moveCategoryUpInEvent($(this))" title="Move category up in this event" style="display:none;"></i></td>
			<td width="25"><i class="fa-solid fa-circle-arrow-down fa-lg cursor-pointer" onclick="moveCategoryDownInEvent($(this))" title="Move category down in this event" style="display:none;"></i></td>
			<td width="25"><i class="fa-solid fa-circle-minus fa-lg cursor-pointer" onclick="removeCategoryFromEvent({{categoryid}})" title="Remove category from this event" style="display:none;"></i></td>
		</tr>
	{{/each}}
</script>
<cfif arguments.event.getValue('eID') GT 0 AND local.isRecurringEvent AND NOT local.inImportEventsQueue AND local.qryRecurringEvents.recordCount>
	<script id="mc_confirmRecurringEventChanges" type="text/html">
		<div class="custom-control custom-radio my-3">
			<input type="radio" id="confirmOpt1" name="confirmOpt" class="custom-control-input" value="single" checked>
			<label class="custom-control-label" for="confirmOpt1">Change for this event only.</label>
		</div>
		<div class="custom-control custom-radio my-3">
			<input type="radio" id="confirmOpt2" name="confirmOpt" class="custom-control-input" value="all">
			<label class="custom-control-label" for="confirmOpt2">Change for this event and all future events in the series.</label>
		</div>
	</script>
</cfif>
</cfoutput>