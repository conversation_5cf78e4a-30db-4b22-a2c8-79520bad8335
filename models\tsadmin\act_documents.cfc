<cfcomponent>

<cffunction name="addDocument" access="private" hint="Saves a new document. Returns documentID." returntype="numeric" output="no">
	<cfargument name="depoMemberDataID" type="numeric" required="true">
	<cfargument name="docState" type="string" required="true">
	<cfargument name="creditType" type="string" required="true">
	<cfargument name="DepoAmazonBucksFullName" type="string" required="true">
	<cfargument name="DepoAmazonBucksEmail" type="string" required="true">
	<cfargument name="isDepoConnectUpload" type="boolean" required="true">
	<cfargument name="originalExt" type="string" required="true">
	<cfargument name="recordedByDepoMemberDataID" type="numeric" required="true">
	
	<cfset var local = structNew()>

	<cfquery name="local.qryAddDepoDocument" datasource="#application.settings.dsn.trialsmith.dsn#">
		SET XACT_ABORT, NOCOUNT ON;
		BEGIN TRY
			DECLARE @documentID int, @uploadSourceID int;

			SELECT @uploadSourceID = uploadSourceID
			FROM dbo.depoDocumentUploadSources
			WHERE sourceName = '#arguments.isDepoConnectUpload ? 'DepoConnect' : 'Admin Uploaded'#';

			EXEC dbo.ts_addDepoDocument
				@depomemberdataID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.depomemberdataID#">,
				@docOrgCode=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.docState#">,
				@originalExt=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.originalExt#">,
				@contributeDate=<cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#now()#">,
				<cfif arguments.creditType EQ "amazon">
					@DepoAmazonBucks=1,
					@DepoAmazonBucksFullName=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.DepoAmazonBucksFullName#">,
					@DepoAmazonBucksEmail=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.DepoAmazonBucksEmail#">,
				<cfelse>
					@DepoAmazonBucks=0,
					@DepoAmazonBucksFullName=NULL,
					@DepoAmazonBucksEmail=NULL,
				</cfif>
				@uploadSourceID=@uploadSourceID,
				@enteredByDepomemberdataID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.recordedByDepoMemberDataID#">,
				@documentID=@documentID OUTPUT;

			SELECT @documentID AS documentID;
		END TRY
		BEGIN CATCH
			IF @@trancount > 0 ROLLBACK TRANSACTION;
			EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
		END CATCH
	</cfquery>

	<cfreturn val(local.qryAddDepoDocument.documentID)>
</cffunction>

<cffunction name="uploadDocument" access="public" hint="uploads a document." returntype="struct" output="no">
	<cfargument name="formfields" type="struct" required="yes">
	
	<cfset var local = structNew()>
	<cfset local.arrUploadedDocs = []>
	<cfset local.uploadDocsResult = { "success":true, "documentid":0, "uploadeddocscount":0, "errmsg":"" }>
	<cfset local.settingsUploadPath = application.settings.DOCS_UPLOADS_PATH>
	
	<!--- Attempt upload of document --->
	<cftry>
		<cffile action="upload" filefield="#arguments.formfields.depoDoc#" destination="#local.settingsUploadPath#" result="local.strUploadResult" nameconflict="makeunique">

		<cfif local.strUploadResult.fileWasSaved>
			<cfif listFindNoCase("rar,zip",local.strUploadResult.serverFileExt)>
				<cfset local.fileName = local.strUploadResult.serverFile>

				<cfset addToDepoDocumentsUnzipQueue(pathToZip="#local.settingsUploadPath##local.strUploadResult.serverFile#", 
					depomemberdataID=arguments.formfields.depoMemberDataID, docState=arguments.formfields.docState, creditType=arguments.formfields.creditType,
					DepoAmazonBucksFullName=arguments.formfields.depoDocAmazonBucksFullName, DepoAmazonBucksEmail=arguments.formfields.depoDocAmazonBucksEmail,
					isDepoConnectUpload=arguments.formfields.isDepoConnectUpload)>
			<cfelse>
				<cfset local.documentID = addDocument(depoMemberDataID=arguments.formfields.depoMemberDataID, docState=arguments.formfields.docState,
					creditType=arguments.formfields.creditType, DepoAmazonBucksFullName=arguments.formfields.depoDocAmazonBucksFullName,
					DepoAmazonBucksEmail=arguments.formfields.depoDocAmazonBucksEmail, isDepoConnectUpload=arguments.formfields.isDepoConnectUpload,
					originalExt=local.strUploadResult.serverFileExt, recordedByDepoMemberDataID=session.depomemberDataID)>
				<cfset local.uploadDocsResult.documentid = local.documentID>
				<cfset local.fileName = "#local.documentID#.#lcase(local.strUploadResult.serverFileExt)#">

				<!--- rename file (ensuring lower case extension) --->
				<cffile action="RENAME" source="#local.settingsUploadPath##local.strUploadResult.serverfile#" destination="#local.settingsUploadPath##local.fileName#">

				<!--- copy file to original docs --->
				<cffile action="COPY" source="#local.settingsUploadPath##local.fileName#" destination="#application.settings.DOCS_ORIGINALS_PATH##local.fileName#">

				<cfif local.strUploadResult.serverFileExt EQ 'pdf'>
					<cfstoredproc procedure="ts_addDepoDocumentToAttachQueue" datasource="#application.settings.dsn.trialsmith.dsn#">
						<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.documentID#">
					</cfstoredproc>
				<cfelse>
					<cfset local.originalDocS3UploadFilePath = "#application.settings.DOCS_ORIGINALS_S3UPLOAD_PATH##local.fileName#">
					<cfset local.s3keyMod = numberFormat(local.documentID mod 1000,"0000")>
					<cfset local.s3objectKey = lcase("depos/original/#local.s3keyMod#/#local.fileName#")>
					<cfset addToS3UploadQueue(filepath=local.originalDocS3UploadFilePath, objectKey=local.s3objectKey)>
				</cfif>
			</cfif>

			<cfset arrayAppend(local.arrUploadedDocs, { "fileName":local.fileName, "fileExt":lcase(local.strUploadResult.serverFileExt) })>
			<cfset local.uploadDocsResult.uploadeddocscount++>
		</cfif>
	<cfcatch type="Any">
		<cfsilent><cf_tlaexception cfcatch="#cfcatch#"></cfsilent>
		<!--- if any errors, delete the uploaded file --->
		<cfif StructKeyExists(local.strUploadResult, "serverfile") AND FileExists("#local.settingsUploadPath##local.strUploadResult.serverfile#")>
			<cffile action="DELETE" file="#local.settingsUploadPath##local.strUploadResult.serverfile#">
		</cfif>
		<cfif StructKeyExists(local.strUploadResult, "serverfile") AND arrayLen(local.arrUploadedDocs)>
			<cfloop array="#local.arrUploadedDocs#" index="local.thisDoc">
				<cfif FileExists("#local.settingsUploadPath##local.thisDoc.fileName#")>
					<cffile action="DELETE" file="#local.settingsUploadPath##local.thisDoc.fileName#">
				</cfif>
			</cfloop>
		</cfif>
		<cfset local.uploadDocsResult["success"] = false>
		<cfset local.uploadDocsResult["errmsg"] = "An error occured while uploading documents.">
	</cfcatch>
	</cftry>
	
	<cfreturn local.uploadDocsResult>
</cffunction>

<cffunction name="addToDepoDocumentsUnzipQueue" access="private" output="false" returntype="void">
	<cfargument name="pathToZip" type="string" required="true">
	<cfargument name="depomemberdataID" type="numeric" required="true">
	<cfargument name="docState" type="string" required="true">
	<cfargument name="creditType" type="string" required="true">
	<cfargument name="DepoAmazonBucksFullName" type="string" required="true">
	<cfargument name="DepoAmazonBucksEmail" type="string" required="true">
	<cfargument name="isDepoConnectUpload" type="boolean" required="true">
	
	<cfset var qryAddToDepoDocumentsUnzipQueue = "">

	<cfquery name="qryAddToDepoDocumentsUnzipQueue" datasource="#application.settings.dsn.trialsmith.dsn#">
		SET XACT_ABORT, NOCOUNT ON;
		BEGIN TRY
			DECLARE @uploadSourceID int;

			SELECT @uploadSourceID = uploadSourceID
			FROM dbo.depoDocumentUploadSources
			WHERE sourceName = '#arguments.isDepoConnectUpload ? 'DepoConnect' : 'Admin Uploaded'#';

			EXEC dbo.ts_addDepoDocumentToUnzipQueue
				@pathToZip=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.pathToZip#">,
				@depomemberdataID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.depomemberdataID#">,
				@docState=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.docState#">,
				<cfif arguments.creditType EQ "amazon">
					@DepoAmazonBucks=1,
					@DepoAmazonBucksFullName=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.DepoAmazonBucksFullName#">,
					@DepoAmazonBucksEmail=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.DepoAmazonBucksEmail#">,
				<cfelse>
					@DepoAmazonBucks=0,
					@DepoAmazonBucksFullName=NULL,
					@DepoAmazonBucksEmail=NULL,
				</cfif>
				@uploadSourceID=@uploadSourceID;
		END TRY
		BEGIN CATCH
			IF @@trancount > 0 ROLLBACK TRANSACTION;
			EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
		END CATCH
	</cfquery>
</cffunction>

<cffunction name="addToS3UploadQueue" access="private" output="false" returntype="void">
	<cfargument name="filepath" type="string" required="true">
	<cfargument name="objectKey" type="string" required="true">

	<cfset var qryAddDocToS3UploadQueue = "">

	<cfquery name="qryAddDocToS3UploadQueue" datasource="#application.settings.dsn.trialsmith.dsn#">
		SET NOCOUNT ON;

		DECLARE @objectKey varchar(400), @s3bucketName varchar(100), @filePath varchar(400),
			@s3UploadReadyStatusID int, @nowDate datetime = getdate();

		SET @s3bucketName = 'trialsmith-depos';
		SET @filePath = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.filepath#">
		SET @objectKey = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.objectKey#">

		SELECT @s3UploadReadyStatusID = qs.queueStatusID
		FROM platformQueue.dbo.tblQueueTypes as qt
		INNER JOIN platformQueue.dbo.tblQueueStatuses as qs on qs.queueTypeID = qt.queueTypeID
		WHERE qt.queueType = 's3Upload'
		AND qs.queueStatus = 'readyToProcess';

		IF NOT EXISTS (SELECT 1 FROM platformQueue.dbo.queue_S3Upload WHERE s3bucketName = @s3bucketName AND objectKey = @objectKey)
			INSERT INTO platformQueue.dbo.queue_S3Upload (statusID, s3bucketName, objectKey, filePath, deleteOnSuccess, dateAdded, dateUpdated)
			VALUES (@s3UploadReadyStatusID, @s3bucketName, @objectKey, @filePath, 1, @nowDate, @nowDate);
	</cfquery>
</cffunction>

<cffunction name="getAllDocumentTypes" access="public" output="no" returntype="query" hint="Returns all document types.">
	<cfset var qryDocTypes = "">

	<cfquery name="qryDocTypes" datasource="#application.settings.dsn.trialsmith.dsn#" cachedwithin="#CreateTimeSpan(0,1,0,0)#">
		SELECT g.categoryID, CASE WHEN ISNULL(g.categoryID,1) = 1 THEN '' ELSE min(g.Description) END AS category, t.TypeID, t.Description
		FROM dbo.depoDocumentTypes t
		LEFT JOIN dbo.depoGroups g ON g.categoryid = t.categoryid
		WHERE updateDisplay = 1 
		AND t.typeid NOT IN (2,9)
		AND (t.categoryID = 3 OR g.categoryID IS NOT NULL)
		GROUP BY g.categoryID, t.TypeID, t.Description
		ORDER BY ISNULL(g.categoryID,1), t.Description
	</cfquery>

	<cfreturn qryDocTypes>
</cffunction>

<cffunction name="getCaseTypeIDByDescription" access="public" output="no" returntype="numeric" hint="Returns all document types.">
	<cfargument name="description" type="string" required="yes">
	
	<cfset var qryCaseTypes = "">

	<cfquery name="local.qryCaseTypes" datasource="#application.settings.dsn.trialsmith.dsn#" cachedwithin="#CreateTimeSpan(0,1,0,0)#">
		SELECT CaseTypeID, Description
		FROM dbo.depoCaseTypes
		WHERE Description = <cfqueryparam value="#arguments.description#" cfsqltype="CF_SQL_VARCHAR">;
	</cfquery>

	<cfreturn val(qryCaseTypes.CaseTypeID)>
</cffunction>

<cffunction name="getDocument" access="public" output="no" returntype="query" hint="Returns a query based on a documentID.">
	<cfargument name="documentID" type="numeric" required="yes">
	
	<cfset var getDocument = "">

	<cfquery name="getDocument" datasource="#application.settings.dsn.trialsmith.dsn#">
		SELECT DISTINCT D.DocumentID, D.DepomemberdataID, D.DocumentTypeID, D.CaseTypeID, D.uploadStatus, D.ExpertName, D.fname, D.mname, D.lname,
			M.FirstName, M.LastName, D.disabled, D.Style, D.DocumentDate, D.State, D.jurisdiction, D.GroupID, D.Notes, D.reviewFlag, D.originalExt,
			D.DateEntered, D.Datelastmodified, D.DepoAmazonBucks, D.DepoAmazonBucksFullName, D.DepoAmazonBucksEmail, D.DepoAmazonBucksCredit,
			ds.statusName AS documentStatusName, D.origHasAttachments, CASE WHEN dfo.documentID IS NOT NULL THEN 1 ELSE 0 END AS XODPreApprove
		FROM dbo.depoDocuments AS D 
		INNER JOIN dbo.depomemberdata AS M ON D.DepomemberdataID = M.depomemberdataID
		INNER JOIN dbo.depoDocumentStatusHistory AS docSH ON docSH.depoDocumentHistoryID = D.currentStatusHistoryID
		INNER JOIN dbo.depoDocumentStatuses AS ds ON ds.statusID = docSH.statusID
		LEFT OUTER JOIN dbo.depoDocumentsFilesOnline as dfo on dfo.documentID = D.documentID and dfo.fileType = 'xodpreapprove'
		WHERE D.DocumentID = <cfqueryparam value="#arguments.documentID#" cfsqltype="CF_SQL_INTEGER">
	</cfquery>

	<cfreturn getDocument>
</cffunction>

<cffunction name="flagAdminReview" access="public" hint="Flags a document for admin review" returntype="void" output="no">
	<cfargument name="formfields" type="struct" required="yes">

	<cfset var qUpdateDoc = "">

	<cfquery name="qUpdateDoc" datasource="#application.settings.dsn.trialsmith.dsn#">
		SET XACT_ABORT, NOCOUNT ON;
		BEGIN TRY

			DECLARE @documentID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.formfields.documentID#">,
				@enteredByDepoMemberDataID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.depomemberDataID#">,
				@oldStatusID int, @statusID int, @currentStatusHistoryID int;

			SELECT @oldStatusID = dsh.statusID
			FROM dbo.depoDocuments AS d
			INNER JOIN dbo.depoDocumentStatusHistory AS dsh ON dsh.depoDocumentHistoryID = d.currentStatusHistoryID
			WHERE d.documentID = @documentID;

			SELECT @statusID = statusID
			FROM dbo.depoDocumentStatuses
			WHERE statusName = 'Flagged for Review';

			BEGIN TRAN;
				UPDATE depoDocuments 
				SET disabled = <cfqueryparam value="Y" cfsqltype="CF_SQL_CHAR">,
					uploadStatus = <cfqueryparam value="1" cfsqltype="CF_SQL_INTEGER">,
					reviewFlag = <cfqueryparam value="1" cfsqltype="CF_SQL_INTEGER">
				WHERE DocumentID = @documentID;

				IF ISNULL(@oldStatusID,0) <> @statusID BEGIN
					INSERT INTO dbo.depoDocumentStatusHistory (documentID, statusID, oldStatusID, enteredByDepoMemberDataID, dateEntered)
					VALUES (@documentID, @statusID, @oldStatusID, @enteredByDepoMemberDataID, GETDATE());
						SET @currentStatusHistoryID = SCOPE_IDENTITY();

					UPDATE dbo.depoDocuments
					SET currentStatusHistoryID = @currentStatusHistoryID
					WHERE DocumentID = @documentID;
				END
			COMMIT TRAN;

		END TRY
		BEGIN CATCH
			IF @@trancount > 0 ROLLBACK TRANSACTION;
			EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
		END CATCH
	</cfquery>
</cffunction>

<cffunction name="saveDocument" access="public" hint="Saves a document" returntype="void" output="no">
	<cfargument name="formfields" type="struct" required="yes">

	<cfset var qUpdateDoc = "">
	<cfset var qSelect = "">

	<cfquery name="qUpdateDoc" datasource="#application.settings.dsn.trialsmith.dsn#">
		SET XACT_ABORT, NOCOUNT ON;
		BEGIN TRY

			DECLARE @documentID int, @GroupID int, @depoMemberDataID int, @docState varchar(10), @depoAmazonBucks bit;
			SET @documentID = <cfqueryparam value="#arguments.formfields.documentID#" cfsqltype="CF_SQL_INTEGER">;
			SET @depoMemberDataID = <cfqueryparam value="#arguments.formfields.depomemberdataid#" cfsqltype="CF_SQL_INTEGER">;
			SET @docState = <cfqueryparam value="#trim(arguments.formfields.State)#" cfsqltype="CF_SQL_VARCHAR">;
			SET @depoAmazonBucks = <cfqueryparam value="#arguments.formfields.DepoAmazonBucks#" cfsqltype="CF_SQL_BIT">;
			SELECT @GroupID = ISNULL(defaultDepoGroupID,0) FROM dbo.depoTLA where State = @docState;
			
			UPDATE dbo.depoDocuments 
			SET GroupID = @GroupID,
				ExpertName = <cfqueryparam value="#trim(arguments.formfields.ExpertName)#" cfsqltype="CF_SQL_VARCHAR">,
				fname = NULLIF(<cfqueryparam value="#trim(arguments.formfields.ExpertFirstName)#" cfsqltype="CF_SQL_VARCHAR">,''),
				mname = NULLIF(<cfqueryparam value="#trim(arguments.formfields.ExpertMiddleName)#" cfsqltype="CF_SQL_VARCHAR">,''),
				lname = NULLIF(<cfqueryparam value="#trim(arguments.formfields.ExpertLastName)#" cfsqltype="CF_SQL_VARCHAR">,''),
				CaseTypeID = <cfqueryparam value="#val(arguments.formfields.CaseType)#" cfsqltype="CF_SQL_INTEGER">,
				STYLE = <cfqueryparam value="#trim(arguments.formfields.Style)#" cfsqltype="CF_SQL_VARCHAR">,
				State = @docState,
				Jurisdiction = <cfqueryparam value="#trim(arguments.formfields.Jurisdiction)#" cfsqltype="CF_SQL_VARCHAR">,
				DocumentTypeID = <cfqueryparam value="#arguments.formfields.DocType#" cfsqltype="CF_SQL_INTEGER">,
				Notes = <cfqueryparam value="#trim(arguments.formfields.Notes)#" cfsqltype="CF_SQL_VARCHAR">,
				depomemberDataID = @depoMemberDataID,
				DateLastmodified = getdate(),
				<cfif isDefined("arguments.formfields.disabled") and arguments.formfields.disabled is 1>
					disabled = <cfqueryparam value="Y" cfsqltype="CF_SQL_CHAR">,
				<cfelse>
					disabled = <cfqueryparam value="N" cfsqltype="CF_SQL_CHAR">,
				</cfif>
				<cfif len(arguments.formfields.DocumentDate) and IsDate(arguments.formfields.DocumentDate)>
					DocumentDate = <cfqueryparam value="#arguments.formfields.DocumentDate#" cfsqltype="CF_SQL_DATE">,
				<cfelse>
					DocumentDate = <cfqueryparam value="" cfsqltype="CF_SQL_DATE" null="Yes">,
				</cfif>
				<cfif arguments.formfields.DepoAmazonBucks>
					DepoAmazonBucksFullName = NULLIF(<cfqueryparam value="#trim(arguments.formfields.DepoAmazonBucksFullName)#" cfsqltype="CF_SQL_VARCHAR">,''),
					DepoAmazonBucksEmail = NULLIF(<cfqueryparam value="#trim(arguments.formfields.DepoAmazonBucksEmail)#" cfsqltype="CF_SQL_VARCHAR">,''),
				</cfif>
				DepoAmazonBucks = @depoAmazonBucks
			WHERE DocumentID = @documentID;

		END TRY
		BEGIN CATCH
			IF @@trancount > 0 ROLLBACK TRANSACTION;
			EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
		END CATCH
	</cfquery>
	
	<!--- Reverse purchase credits if disabled --->
	<cfif isDefined("arguments.formfields.disabled") and arguments.formfields.disabled is 1>
		<cfset reversePurchaseCredits(arguments.formfields.depomemberdataid,arguments.formfields.documentID)>
	</cfif>
	
	<!--- Move file out of uploads --->
	<cfquery name="qSelect" datasource="#application.settings.dsn.trialsmith.dsn#">
		Select DocumentID, originalExt
		From depoDocuments
		Where DocumentID = <cfqueryparam value="#arguments.formfields.documentID#" cfsqltype="CF_SQL_INTEGER">
	</cfquery>
	<cftry>
		<cfif fileExists("#application.settings.DOCS_UPLOADS_PATH##qSelect.DocumentID#.#qSelect.originalExt#")>
			<cffile action="copy" source="#application.settings.DOCS_UPLOADS_PATH##qSelect.DocumentID#.#qSelect.originalExt#"
					destination="#application.settings.DOCS_DELETEDUPLOADS_PATH##qSelect.DocumentID#.#qSelect.originalExt#">
			<cffile action="delete" file="#application.settings.DOCS_UPLOADS_PATH##qSelect.DocumentID#.#qSelect.originalExt#">
			<cfset local.originalDocS3UploadFilePath = "#application.settings.DOCS_DELETEDUPLOADS_PATH##qSelect.DocumentID#.#qSelect.originalExt#">
			<cfset local.s3objectKey = lcase("depos/deleteduploads/#local.documentID#.#local.strUploadResult.serverFileExt#")>
			<cfif application.settings.environment eq "production">
				<cfset addToS3UploadQueue(filepath=local.originalDocS3UploadFilePath, objectKey=local.s3objectKey)>
			</cfif>			
		</cfif>
	<cfcatch type="Any">
	</cfcatch>
	</cftry>
</cffunction>

<cffunction name="approveDocument" access="public" hint="Approves a document" returntype="void" output="no">
	<cfargument name="formfields" type="struct" required="yes">

	<cfset var local = StructNew()>
	
	<cfquery name="local.qryDoc" datasource="#application.settings.dsn.trialsmith.dsn#">
		SELECT DocumentID, originalExt
		FROM dbo.depoDocuments
		WHERE DocumentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.formfields.documentID#">
	</cfquery>

	<cfset local.documentID = val(local.qryDoc.documentID)>
	<cfset local.s3keyMod = numberFormat(local.documentID mod 1000,"0000")>

	<cfif arguments.formfields.docApprovalOpt EQ 'original'>
		<cfset local.copyFromS3ObjectKey = lcase("depos/original/#local.s3keyMod#/#local.documentID#.#local.qryDoc.originalExt#")>
		<cfset local.copyToS3ObjectKey = lcase("depos/documenttoprocess/#local.s3keyMod#/#local.documentID#.#local.qryDoc.originalExt#")>
	<cfelseif arguments.formfields.docApprovalOpt EQ 'new'>
		<cfset local.settingsUploadPath = application.settings.DOCS_UPLOADS_PATH>

		<!--- upload document --->
		<cffile action="upload" filefield="newDepoDocFile" destination="#local.settingsUploadPath#" result="local.strUploadResult" nameconflict="makeunique">

		<!--- update doc originalExt --->
		<cfquery name="local.qryUpdateDoc" datasource="#application.settings.dsn.trialsmith.dsn#">
			UPDATE dbo.depoDocuments
			SET originalExt = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strUploadResult.serverFileExt#">,
				DateLastmodified = GETDATE()
			WHERE DocumentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.documentID#">
		</cfquery>

		<cfset local.serverUploadedFilePath = "#local.settingsUploadPath##local.strUploadResult.serverfile#">
		<cfset local.uploadedFilePath = "#local.settingsUploadPath##local.documentID#.#local.strUploadResult.serverFileExt#">
		<cfset local.originalDocFilePath = "#application.settings.DOCS_ORIGINALS_PATH##local.documentID#.#local.strUploadResult.serverFileExt#">

		<!--- rename file to documentID --->
		<cffile action="RENAME" source="#local.serverUploadedFilePath#" destination="#local.uploadedFilePath#">
		<!--- copy file to original docs --->
		<cffile action="COPY" source="#local.uploadedFilePath#" destination="#local.originalDocFilePath#">

		<cfset local.originalDocS3UploadFilePath = "#application.settings.DOCS_ORIGINALS_S3UPLOAD_PATH##local.documentID#.#local.strUploadResult.serverFileExt#">
		<cfset local.s3objectKey = lcase("depos/documenttoprocess/#local.s3keyMod#/#local.documentID#.#local.strUploadResult.serverFileExt#")>
		<cfif application.settings.environment eq "production">
			<cfset addToS3UploadQueue(filepath=local.originalDocS3UploadFilePath, objectKey=local.s3objectKey)>
		</cfif>
	<cfelseif arguments.formfields.docApprovalOpt EQ 'merge'>
		<cfset local.sqsMessage = { 
			"sourcebucket": "trialsmith-depos",
			"destinationbucket": "trialsmith-depos",
			"destinationkey": "depos/documenttoprocess/#local.s3keyMod#/#local.documentID#.pdf",
			"arrS3Keys": []
		}>
		<cfset local.arrSortOrder = deserializeJSON(arguments.formfields.newsortorder)>
		<cfloop array="#local.arrSortOrder#" item="local.thisFile">
			<cfif local.thisFile.type eq "attach">
				<cfset local.sqsMessage.arrS3Keys.append("depos/approvals/#local.s3keyMod#/#local.documentID#/#lcase(ToString(ToBinary(local.thisFile.atb64)))#.pdf")>
			<cfelseif local.thisFile.type eq "orig">
				<cfset local.sqsMessage.arrS3Keys.append("depos/approvals/#local.s3keyMod#/#local.documentID#.pdf")>
			</cfif>
		</cfloop>

		<cfif application.settings.environment eq "production">
			<cfset local.sqsResult = application.objAWS_SQS.sqs.sendMessage(queueName="839440652392/mergePDFs", message=serializeJSON(local.sqsMessage))>
			<cfif local.sqsResult.statusCode neq "200">
				<cfoutput>Error merging into one PDF.</cfoutput>
				<cfabort>
			</cfif>
		</cfif>
	</cfif>
	
	<!--- set uploadStatus flag --->
	<cfquery name="local.approvedoc" datasource="#application.settings.dsn.trialsmith.dsn#">
		SET XACT_ABORT, NOCOUNT ON;
		BEGIN TRY

			DECLARE @documentID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.formfields.documentID#">,
				@enteredByDepoMemberDataID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.depomemberDataID#">,
				@oldStatusID int, @statusID int, @currentStatusHistoryID int, @nowDate datetime = GETDATE();

			SELECT @oldStatusID = dsh.statusID
			FROM dbo.depoDocuments AS d
			INNER JOIN dbo.depoDocumentStatusHistory AS dsh ON dsh.depoDocumentHistoryID = d.currentStatusHistoryID
			WHERE d.documentID = @documentID;

			SELECT @statusID = statusID
			FROM dbo.depoDocumentStatuses
			WHERE statusName = 'Approved';

			<cfif arguments.formfields.docApprovalOpt EQ 'original'>
				DECLARE @objectkey varchar(200) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.copyFromS3ObjectKey#">,
					@newObjectKey varchar(200) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.copyToS3ObjectKey#">,
					@s3CopyReadyStatusID int, @s3bucketName varchar(100) = 'trialsmith-depos';

				SELECT @s3CopyReadyStatusID = qs.queueStatusID
				FROM platformQueue.dbo.tblQueueTypes as qt
				INNER JOIN platformQueue.dbo.tblQueueStatuses as qs on qs.queueTypeID = qt.queueTypeID
				WHERE qt.queueType = 's3Copy'
				AND qs.queueStatus = 'readyToProcess';
			</cfif>

			BEGIN TRAN;
				UPDATE depoDocuments 
				SET uploadStatus = 2
				WHERE DocumentID = @documentID;

				IF ISNULL(@oldStatusID,0) <> @statusID BEGIN
					INSERT INTO dbo.depoDocumentStatusHistory (documentID, statusID, oldStatusID, enteredByDepoMemberDataID, dateEntered)
					VALUES (@documentID, @statusID, @oldStatusID, @enteredByDepoMemberDataID, @nowDate);
						SET @currentStatusHistoryID = SCOPE_IDENTITY();

					UPDATE dbo.depoDocuments
					SET currentStatusHistoryID = @currentStatusHistoryID
					WHERE DocumentID = @documentID;
				END

				<cfif application.settings.environment eq "production" and arguments.formfields.docApprovalOpt EQ 'original'>
					IF NOT EXISTS (SELECT 1 FROM platformQueue.dbo.queue_S3Copy WHERE s3bucketName = @s3bucketName AND objectKey = @objectKey AND newObjectKey = @newObjectKey)
						INSERT INTO platformQueue.dbo.queue_S3Copy (s3bucketName, objectkey, newS3bucketName, newObjectKey, dateAdded, dateUpdated, nextAttemptDate, statusID)
						VALUES (@s3bucketName, @objectkey, @s3bucketName, @newObjectKey, @nowDate, @nowDate, @nowDate, @s3CopyReadyStatusID);
				</cfif>
			COMMIT TRAN;

		END TRY
		BEGIN CATCH
			IF @@trancount > 0 ROLLBACK TRANSACTION;
			EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
		END CATCH
	</cfquery>
	
	<!--- Assign purchase credits --->
	<cfscript>
	assignPurchaseCredits(val(arguments.formfields.DocType),arguments.formfields.StateCredit,arguments.formfields.MemberCredit,arguments.formfields.depoMemberDataID,arguments.formfields.documentID,arguments.formfields.DocumentDate);
	</cfscript>
</cffunction>
	
<cffunction name="denyDocument" access="public" hint="Denies a document" returntype="void" output="no">
	<cfargument name="formfields" type="struct" required="yes">

	<cfset var local = StructNew()>
	
	<!--- set uploadStatus flag --->
	<cfquery name="local.qryDenyDoc" datasource="#application.settings.dsn.trialsmith.dsn#">
		SET XACT_ABORT, NOCOUNT ON;
		BEGIN TRY

			DECLARE @documentID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.formfields.documentID#">,
				@enteredByDepoMemberDataID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.depomemberDataID#">,
				@oldStatusID int, @statusID int, @currentStatusHistoryID int;

			SELECT @oldStatusID = dsh.statusID
			FROM dbo.depoDocuments AS d
			INNER JOIN dbo.depoDocumentStatusHistory AS dsh ON dsh.depoDocumentHistoryID = d.currentStatusHistoryID
			WHERE d.documentID = @documentID;

			SELECT @statusID = statusID
			FROM dbo.depoDocumentStatuses
			WHERE statusName = 'Denied';

			BEGIN TRAN;
				UPDATE depoDocuments 
				SET uploadStatus = 2,
					reviewFlag = 0,
					disabled = 'Y'
				WHERE DocumentID = @documentID;

				IF ISNULL(@oldStatusID,0) <> @statusID BEGIN
					INSERT INTO dbo.depoDocumentStatusHistory (documentID, statusID, oldStatusID, enteredByDepoMemberDataID, dateEntered)
					VALUES (@documentID, @statusID, @oldStatusID, @enteredByDepoMemberDataID, GETDATE());
						SET @currentStatusHistoryID = SCOPE_IDENTITY();

					UPDATE dbo.depoDocuments
					SET currentStatusHistoryID = @currentStatusHistoryID
					WHERE DocumentID = @documentID;
				END
			COMMIT TRAN;

		END TRY
		BEGIN CATCH
			IF @@trancount > 0 ROLLBACK TRANSACTION;
			EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
		END CATCH
	</cfquery>

	<!--- Reverse purchase credits --->
	<cfset reversePurchaseCredits(arguments.formfields.depoMemberDataID,arguments.formfields.documentID)>
</cffunction>

<cffunction name="returnDocumentToPending" access="public" hint="Returns a document to the pending queue" returntype="void" output="no">
	<cfargument name="formfields" type="struct" required="yes">

	<cfset var returndoc = "">
	
	<cfquery name="returndoc" datasource="#application.settings.dsn.trialsmith.dsn#">
		SET XACT_ABORT, NOCOUNT ON;
		BEGIN TRY

			DECLARE @documentID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.formfields.documentID#">,
				@enteredByDepoMemberDataID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.depomemberDataID#">,
				@oldStatusID int, @statusID int, @currentStatusHistoryID int;

			SELECT @oldStatusID = dsh.statusID
			FROM dbo.depoDocuments AS d
			INNER JOIN dbo.depoDocumentStatusHistory AS dsh ON dsh.depoDocumentHistoryID = d.currentStatusHistoryID
			WHERE d.documentID = @documentID;

			SELECT @statusID = statusID
			FROM dbo.depoDocumentStatuses
			WHERE statusName = 'Pending Approval';

			BEGIN TRAN;
				UPDATE depoDocuments 
				SET uploadStatus = <cfqueryparam value="1" cfsqltype="CF_SQL_INTEGER">,
					reviewFlag = <cfqueryparam value="0" cfsqltype="CF_SQL_INTEGER">,
					disabled = <cfqueryparam value="N" cfsqltype="CF_SQL_CHAR">
				WHERE DocumentID = @documentID;

				IF ISNULL(@oldStatusID,0) <> @statusID BEGIN
					INSERT INTO dbo.depoDocumentStatusHistory (documentID, statusID, oldStatusID, enteredByDepoMemberDataID, dateEntered)
					VALUES (@documentID, @statusID, @oldStatusID, @enteredByDepoMemberDataID, GETDATE());
						SET @currentStatusHistoryID = SCOPE_IDENTITY();

					UPDATE dbo.depoDocuments
					SET currentStatusHistoryID = @currentStatusHistoryID
					WHERE DocumentID = @documentID;
				END
			COMMIT TRAN;

		END TRY
		BEGIN CATCH
			IF @@trancount > 0 ROLLBACK TRANSACTION;
			EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
		END CATCH
	</cfquery>
</cffunction>

<cffunction name="reversePurchaseCredits" access="private" hint="Reverses purchase credits for a document" returntype="void" output="no">
	<cfargument name="depoMemberDataID" type="numeric" required="yes">
	<cfargument name="documentID" type="numeric" required="yes">

	<cfset var qTotalPC = "">
	<cfset var reversePC = "">

	<cfquery name="qTotalPC" datasource="#application.settings.dsn.trialsmith.dsn#">
		Select DepoMemberDataID, sum(PurchaseCreditAmount) as Total, sum(Statecreditamount) as StateTotal
		From PurchaseCredits
		Where DocumentID = <cfqueryparam value="#arguments.documentID#" cfsqltype="CF_SQL_INTEGER">
		and DepoMemberDataID = <cfqueryparam value="#arguments.DepoMemberDataID#" cfsqltype="CF_SQL_INTEGER">
		Group By DepoMemberDataID
	</cfquery>
	<cfif qTotalPC.total gt 0>
		<cfquery name="reversePC" datasource="#application.settings.dsn.trialsmith.dsn#">
			insert into purchasecredits (depoMemberDataID, creditdate, purchasecreditamount, creditdescription, documentid)
			values (
				<cfqueryparam value="#arguments.depoMemberDataID#" cfsqltype="CF_SQL_INTEGER">,
				getdate(),
				#qTotalPC.total * -1#,
				<cfqueryparam value="Remove credit due to document being disabled" cfsqltype="CF_SQL_VARCHAR">,
				<cfqueryparam value="#arguments.documentID#" cfsqltype="CF_SQL_INTEGER">
			)
		</cfquery>
	</cfif>
</cffunction>

<cffunction name="assignPurchaseCredits" access="private" hint="Assigns purchase credits for a document" returntype="void" output="no">
	<cfargument name="DocType" type="numeric" required="yes">
	<cfargument name="StateCredit" type="string" required="yes">
	<cfargument name="MemberCredit" type="string" required="yes">
	<cfargument name="depoMemberDataID" type="numeric" required="yes">
	<cfargument name="documentID" type="numeric" required="yes">
	<cfargument name="documentDate" type="string" required="yes">

	<cfset var local = StructNew()>

	<cfquery name="local.qCredit" datasource="#application.settings.dsn.trialsmith.dsn#">
		select StateCreditAmount, PurchaseCreditAmount, TransactionCreditAllowed
		from dbo.depodocumenttypes
		where typeid = <cfqueryparam value="#arguments.DocType#" cfsqltype="CF_SQL_INTEGER">
	</cfquery>
	<cfquery name="local.qryDepoDocAmazonCredits" datasource="#application.settings.dsn.trialsmith.dsn#">
		SELECT documentID
		FROM dbo.depoDocuments
		WHERE documentID = <cfqueryparam  cfsqltype="CF_SQL_INTEGER"value="#arguments.documentID#">
		AND DepoAmazonBucks = 1
	</cfquery>
	<cfif arguments.StateCredit EQ "NO">
		<cfset local.vStateCredit = "">
		<cfset local.vStateCreditAmount = 0>
	<cfelse>
		<cfset local.vStateCredit = arguments.StateCredit>
		<cfset local.vStateCreditAmount = local.qCredit.StateCreditAmount>
	</cfif>

	<cfset local.thisDateDiff = dateDiff("d", arguments.documentDate, dateFormat(now(),"mm/dd/yyyy"))>

	<cfif arguments.MemberCredit eq "PC" AND local.qryDepoDocAmazonCredits.recordCount>
		<cfif local.thisDateDiff lte 365>
			<cfset local.PurchaseCreditAmount = 10>
		<cfelseif local.thisDateDiff lte 1825 and local.thisDateDiff gt 365>
			<cfset local.PurchaseCreditAmount = 7>
		<cfelse>
			<cfset local.PurchaseCreditAmount = 5>
		</cfif>
		
		<cfquery name="local.qryAddAmazonCredits" datasource="#application.settings.dsn.trialsmith.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @documentID int, @depoMemberDataID int, @creditAmount decimal(18,2);
				SET @documentID = <cfqueryparam value="#arguments.documentID#" cfsqltype="CF_SQL_INTEGER">;
				SET @depoMemberDataID = <cfqueryparam value="#arguments.depoMemberDataID#" cfsqltype="CF_SQL_INTEGER">;
				SET @creditAmount = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.PurchaseCreditAmount#">;

				BEGIN TRAN;
					UPDATE dbo.depoDocuments 
					SET DepoAmazonBucksCredit = @creditAmount
					WHERE DocumentID = @documentID;

					INSERT INTO dbo.PurchaseCredits (depoMemberDataID, CreditDate, CreditDescription, DocumentID, AmazonBucksCreditAmount)
					VALUES (@depoMemberDataID, GETDATE(), 'Amazon Bucks Credits', @documentID, @creditAmount);
				COMMIT TRAN;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	<cfelseif arguments.MemberCredit eq "PC" AND local.qCredit.PurchaseCreditAmount GT 0>
		<cfset local.PurchaseCreditAmount = local.qCredit.PurchaseCreditAmount>
		
		<cfif val(arguments.DocType) eq 1>
			<cfif len(trim(arguments.documentDate)) and local.thisDateDiff gt 1825>
				<cfset local.PurchaseCreditAmount = local.PurchaseCreditAmount * 2>
			<cfelseif len(trim(arguments.documentDate)) and  local.thisDateDiff lte 1825 and local.thisDateDiff gt 365>
				<cfset local.PurchaseCreditAmount = local.PurchaseCreditAmount * 4>
			<cfelseif not len(trim(arguments.documentDate)) or local.thisDateDiff lte 365>
				<cfset local.PurchaseCreditAmount = local.PurchaseCreditAmount * 6>
			</cfif>
		</cfif>
		<cfquery name="local.qCase3" datasource="#application.settings.dsn.trialsmith.dsn#">
			insert into PurchaseCredits (depoMemberDataID, StateToCredit, StateCreditAmount, PurchaseCreditAmount, CreditDescription, DocumentID)
			values(
				<cfqueryparam value="#arguments.depoMemberDataID#" cfsqltype="CF_SQL_INTEGER">,
				<cfqueryparam value="#local.vStateCredit#" cfsqltype="CF_SQL_VARCHAR">,
				<cfqueryparam value="#local.vStateCreditAmount#" cfsqltype="CF_SQL_FLOAT">,
				<cfqueryparam value="#local.PurchaseCreditAmount#" cfsqltype="CF_SQL_FLOAT">,
				<cfqueryparam value="Credit Applied for Contributed Document" cfsqltype="CF_SQL_VARCHAR">,
				<cfqueryparam value="#arguments.documentID#" cfsqltype="CF_SQL_INTEGER">
			)
		</cfquery>
	<cfelseif arguments.MemberCredit eq "CD" AND local.qCredit.PurchaseCreditAmount GT 0>
		<cfset local.vDescription = "Credit Applied for Document " & arguments.documentID & "; CDROM requested instead of Purchase Credit">
		<cfquery name="local.qCase4" datasource="#application.settings.dsn.trialsmith.dsn#">
			insert into PurchaseCredits (depoMemberDataID, StateToCredit, StateCreditAmount, PurchaseCreditAmount, CreditDescription, DocumentID)
			values(
				<cfqueryparam value="#arguments.depoMemberDataID#" cfsqltype="CF_SQL_INTEGER">,
				<cfqueryparam value="#local.vStateCredit#" cfsqltype="CF_SQL_VARCHAR">,
				<cfqueryparam value="#local.vStateCreditAmount#" cfsqltype="CF_SQL_FLOAT">,
				<cfqueryparam value="0" cfsqltype="CF_SQL_FLOAT">,
				<cfqueryparam value="#local.vDescription#" cfsqltype="CF_SQL_VARCHAR">,
				<cfqueryparam value="#arguments.documentID#" cfsqltype="CF_SQL_INTEGER">
			)
		</cfquery>
	<cfelse>
		<cfquery name="local.qCase9" datasource="#application.settings.dsn.trialsmith.dsn#">
			insert into PurchaseCredits (depoMemberDataID, StateToCredit, StateCreditAmount, PurchaseCreditAmount, CreditDescription, DocumentID)
			values(
				<cfqueryparam value="#arguments.depoMemberDataID#" cfsqltype="CF_SQL_INTEGER">,
				<cfqueryparam value="" cfsqltype="CF_SQL_VARCHAR">,
				<cfqueryparam value="0" cfsqltype="CF_SQL_FLOAT">,
				<cfqueryparam value="0" cfsqltype="CF_SQL_FLOAT">,
				<cfqueryparam value="Document received but type of document not entitled to purchase credit" cfsqltype="CF_SQL_VARCHAR">,
				<cfqueryparam value="#arguments.documentID#" cfsqltype="CF_SQL_INTEGER">
			)
		</cfquery>
	</cfif>
</cffunction>

<cffunction name="getMatchingDocuments" access="public" hint="Returns a query of matching documents" returntype="query" output="no">
	<cfargument name="expertFirstName" type="string" required="yes">
	<cfargument name="expertLastName" type="string" required="yes">
	<cfargument name="depositionDate" type="string" required="yes">
	<cfargument name="excludeDocumentID" type="numeric" required="no" default="0">

	<cfset var qryMatching = "">

	<!--- Added disabled clause 3/15/06 - tl - kristie doesnt want disabled docs to show --->
	<cfquery name="qryMatching" datasource="#application.settings.dsn.trialsmith.dsn#">
		SELECT dd.DocumentID, dd.depoMemberDataID, dd.ExpertName, dd.DocumentDate,
			CASE WHEN dd.uploadStatus = 2 AND dd.disabled = 'N' THEN 1 ELSE 0 END AS IsApproved,
			dmd.FirstName, dmd.MiddleName, dmd.LastName, dmd.SourceID, ds.statusName AS documentStatusName
		FROM dbo.depoDocuments AS dd
		INNER JOIN dbo.depomemberdata AS dmd ON dmd.depomemberdataID = dd.DepomemberdataID
		INNER JOIN dbo.depoDocumentStatusHistory AS docSH ON docSH.depoDocumentHistoryID = dd.currentStatusHistoryID
		INNER JOIN dbo.depoDocumentStatuses AS ds ON ds.statusID = docSH.statusID
		WHERE dd.disabled = 'N'
		<cfif arguments.excludeDocumentID gt 0>
			AND dd.DocumentID <> <cfqueryparam value="#arguments.excludeDocumentID#" cfsqltype="CF_SQL_INTEGER">
		</cfif>
		AND dd.ExpertName like <cfqueryparam value="%#trim(request.expertFirstName)#%" cfsqltype="CF_SQL_VARCHAR">
		AND dd.ExpertName like <cfqueryparam value="%#trim(request.expertLastName)#%" cfsqltype="CF_SQL_VARCHAR">
		AND convert(varchar(20),dd.DocumentDate,101) = <cfqueryparam value="#arguments.depositionDate#" cfsqltype="CF_SQL_DATE">
		ORDER BY documentID
	</cfquery>

	<cfreturn qryMatching>
</cffunction>

<cffunction name="getMatchingDocumentsInfo" access="public" hint="Returns an of matching documents info" returntype="array" output="no">
	<cfargument name="qryDocuments" type="query" required="yes">

	<cfset var local = structNew()>
	<cfset var arrDocumentsInfo = []>
	<cfset local.objTSDocument = CreateObject('component','models.trialsmith.tsDocument')>

	<cfloop query="arguments.qryDocuments">
		<cfset local.canViewDocument = false>
		<cfset local.documentLink = false>
		<cfset local.documentEncPassword = false>
		<cfif listFindNoCase("Pending Approval,Approved",arguments.qryDocuments.documentStatusName)>
			<cfset local.s3DocStruct = local.objTSDocument.getDocumentInfoForWebViewer(docID=val(arguments.qryDocuments.documentID))>
			<cfset local.canViewDocument = local.s3DocStruct.documentID gt 0 and local.s3DocStruct.result eq "viewingAllowed">
		</cfif>
		
		<cfset local.matchingDocStruct = {
			"documentID": arguments.qryDocuments.documentID,
			"depoMemberDataID": arguments.qryDocuments.depoMemberDataID,
			"documentDate": arguments.qryDocuments.DocumentDate,
			"expertName": arguments.qryDocuments.ExpertName,
			"contributorName": arguments.qryDocuments.FirstName & " " & arguments.qryDocuments.LastName,
			"SourceID": arguments.qryDocuments.SourceID,
			"canViewDocument": local.canViewDocument,
			"documentS3Link": local.canViewDocument ? local.s3DocStruct.s3Link : "",
			"documentEncPassword": local.canViewDocument ? local.s3DocStruct.encPass : ""
		}>

		<cfset arrayAppend(arrDocumentsInfo, local.matchingDocStruct)>
	</cfloop>

	<cfreturn arrDocumentsInfo>
</cffunction>

<cffunction name="getDocumentsPending" access="public" hint="Returns the number of documents pending approval." returntype="numeric" output="no">
	<cfset var local = structNew()>

	<cfset local.pendingStatusID = getDocumentStatuses().filter(function(thisRow) { return arguments.thisRow.statusName EQ 'Pending Approval'; }).statusID>

	<cfquery name="local.qryDocuments" datasource="#application.settings.dsn.trialsmith.dsn#" cachedwithin="#createTimeSpan(0,0,5,0)#">
		SET NOCOUNT ON;
		SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

		SELECT COUNT(d.DocumentID) as DocCount
		FROM dbo.depoDocuments AS d
		INNER JOIN dbo.depoDocumentStatusHistory AS docSH ON docSH.depoDocumentHistoryID = d.currentStatusHistoryID
			AND docSH.statusID = #local.pendingStatusID#;

		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	</cfquery>

	<cfreturn val(local.qryDocuments.DocCount)>
</cffunction>

<cffunction name="getNextDocumentPending" access="public" hint="Returns the next document pending approval." returntype="query" output="no">
	<cfargument name="documentID" type="numeric" required="yes">

	<cfset var local = structNew()>
	
	<cfset local.strDocsQueueFilter = getDocumentsQueueFilter()>
	<cfset local.qryDocumentStatuses = getDocumentStatuses()>
	<cfset local.pendingStatusID = QueryFilter(local.qryDocumentStatuses, function(thisRow) { return arguments.thisRow.statusName EQ 'Pending Approval'; }).statusID>

	<cfset local.qryNextDocument = getFilteredDocuments(contributorFirstName=local.strDocsQueueFilter.contributorFirstName, 
		contributorLastName=local.strDocsQueueFilter.contributorLastName, depoMemberDataID=local.strDocsQueueFilter.depoMemberDataID, 
		expertFirstName=local.strDocsQueueFilter.expertFirstName, expertLastName=local.strDocsQueueFilter.expertLastName, 
		expertNameContains=local.strDocsQueueFilter.expertNameContains, documentID=local.strDocsQueueFilter.documentID, 
		style=local.strDocsQueueFilter.style, orgCode=local.strDocsQueueFilter.orgCode, 
		depositionDateFrom=local.strDocsQueueFilter.depositionDateFrom, depositionDateTo=local.strDocsQueueFilter.depositionDateTo, 
		dateEnteredFrom=local.strDocsQueueFilter.dateEnteredFrom, dateEnteredTo=local.strDocsQueueFilter.dateEnteredTo, 
		statusSetFrom=local.strDocsQueueFilter.statusSetFrom, statusSetTo=local.strDocsQueueFilter.statusSetTo, 
		documentStatusID=local.pendingStatusID, docHasAttachments=local.strDocsQueueFilter.docHasAttachments,
		documentExtension=local.strDocsQueueFilter.documentExtension, xodpreapprove=local.strDocsQueueFilter.xodpreapprove, 
		xodapprove=local.strDocsQueueFilter.xodapprove, uploadSourceID=local.strDocsQueueFilter.uploadSourceID,
		hasAmazonCredits=local.strDocsQueueFilter.hasAmazonCredits, orderBy=local.strDocsQueueFilter.orderBy, 
		orderDir=local.strDocsQueueFilter.orderDir, count=1, resultsMode="list")>

	<cfreturn local.qryNextDocument>
</cffunction>

<cffunction name="getDocumentsForAdminReview" access="public" hint="Returns a query of documents flagged for admin review." returntype="numeric" output="no">
	<cfset var local = structNew()>

	<cfset local.adminReviewStatusID = getDocumentStatuses().filter(function(thisRow) { return arguments.thisRow.statusName EQ 'Flagged for Review'; }).statusID>

	<cfquery name="local.qryDocuments" datasource="#application.settings.dsn.trialsmith.dsn#" cachedwithin="#createTimeSpan(0,0,2,0)#">
		SET NOCOUNT ON;
		SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

		SELECT COUNT(d.DocumentID) as DocCount
		FROM dbo.depoDocuments AS d
		INNER JOIN dbo.depoDocumentStatusHistory AS docSH ON docSH.depoDocumentHistoryID = d.currentStatusHistoryID
			AND docSH.statusID = #local.adminReviewStatusID#;

		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	</cfquery>

	<cfreturn val(local.qryDocuments.DocCount)>
</cffunction>

<cffunction name="getDocumentsForApprovedNoXOD" access="public" hint="Returns a query of documents Approved NO XOD." returntype="numeric" output="no">
	<cfset var local = structNew()>

	<cfset local.approvedStatusID = getDocumentStatuses().filter(function(thisRow) { return arguments.thisRow.statusName EQ 'Approved'; }).statusID>

	<cfquery name="local.qryDocuments" datasource="#application.settings.dsn.trialsmith.dsn#" cachedwithin="#createTimeSpan(0,0,2,0)#">
		SET NOCOUNT ON;
		SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

		SELECT COUNT(d.DocumentID) as DocCount
		FROM dbo.depoDocuments AS d
		INNER JOIN dbo.depoDocumentStatusHistory AS docSH ON docSH.depoDocumentHistoryID = d.currentStatusHistoryID AND docSH.statusID = <cfqueryparam value="#val(local.approvedStatusID)#" cfsqltype="CF_SQL_INTEGER">
		LEFT OUTER JOIN dbo.depoDocumentsFilesOnline as dfoXF on dfoXF.documentID = d.documentID and dfoXF.fileType = 'xodfull'
		WHERE 1 = 1
		AND d.DateEntered >= <cfqueryparam value="#dateFormat(dateAdd("yyyy", -1, now()), "mm/dd/yyyy")#" cfsqltype="CF_SQL_DATE">
		AND d.disabled <> <cfqueryparam value="Y" cfsqltype="CF_SQL_VARCHAR">			
		AND d.originalExt = <cfqueryparam value="PDF" cfsqltype="CF_SQL_VARCHAR">
		AND dfoXF.depoFileID is null

		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	</cfquery>

	<cfreturn val(local.qryDocuments.DocCount)>
</cffunction>

<cffunction name="getNextDocumentForAdminReview" access="public" hint="Returns the next document pending approval." returntype="query" output="no">
	<cfargument name="documentID" type="numeric" required="yes">

	<cfset var local = structNew()>
	
	<cfset local.strDocsQueueFilter = getDocumentsQueueFilter()>
	<cfset local.qryDocumentStatuses = getDocumentStatuses()>
	<cfset local.adminReviewStatusID = QueryFilter(local.qryDocumentStatuses, function(thisRow) { return arguments.thisRow.statusName EQ 'Flagged for Review'; }).statusID>

	<cfset local.qryNextDocument = getFilteredDocuments(contributorFirstName=local.strDocsQueueFilter.contributorFirstName, 
		contributorLastName=local.strDocsQueueFilter.contributorLastName, depoMemberDataID=local.strDocsQueueFilter.depoMemberDataID, 
		expertFirstName=local.strDocsQueueFilter.expertFirstName, expertLastName=local.strDocsQueueFilter.expertLastName, 
		expertNameContains=local.strDocsQueueFilter.expertNameContains, documentID=local.strDocsQueueFilter.documentID, 
		style=local.strDocsQueueFilter.style, orgCode=local.strDocsQueueFilter.orgCode, 
		depositionDateFrom=local.strDocsQueueFilter.depositionDateFrom, depositionDateTo=local.strDocsQueueFilter.depositionDateTo, 
		dateEnteredFrom=local.strDocsQueueFilter.dateEnteredFrom, dateEnteredTo=local.strDocsQueueFilter.dateEnteredTo, 
		statusSetFrom=local.strDocsQueueFilter.statusSetFrom, statusSetTo=local.strDocsQueueFilter.statusSetTo, 
		documentStatusID=local.adminReviewStatusID, docHasAttachments=local.strDocsQueueFilter.docHasAttachments, 
		documentExtension=local.strDocsQueueFilter.documentExtension, xodpreapprove=local.strDocsQueueFilter.xodpreapprove, 
		xodapprove=local.strDocsQueueFilter.xodapprove, uploadSourceID=local.strdocsQueueFilter.uploadSourceID,
		hasAmazonCredits=local.strDocsQueueFilter.hasAmazonCredits, orderBy=local.strDocsQueueFilter.orderBy, 
		orderDir=local.strDocsQueueFilter.orderDir, count=1, resultsMode="list")>

	<cfreturn local.qryNextDocument>
</cffunction>

<cffunction name="changeDocumentContributor" access="public" hint="Changes a document's contributor" returntype="struct" output="no">
	<cfargument name="documentID" type="numeric" required="yes">
	<cfargument name="depomemberdataID" type="numeric" required="yes">

	<cfset var local = StructNew()>
	
	<cftransaction>
	<cfquery name="local.qryGetNewAccount" datasource="#application.settings.dsn.trialsmith.dsn#">
		select top 1 sourceid, depomemberdataID
		from depomemberdata
		where depomemberdataID = <cfqueryparam value="#arguments.depomemberdataID#" cfsqltype="CF_SQL_INTEGER">
	</cfquery>
	<cfif local.qryGetNewAccount.recordcount>
		<cfquery name="local.qryGetOrigID" datasource="#application.settings.dsn.trialsmith.dsn#">
			select depomemberdataID
			from depodocuments
			where documentID = <cfqueryparam value="#arguments.documentID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		<cfquery name="local.qryUpdate" datasource="#application.settings.dsn.trialsmith.dsn#">
			update depodocuments
			set depomemberdataID = <cfqueryparam value="#local.qryGetNewAccount.depomemberdataID#" cfsqltype="CF_SQL_INTEGER">
			where documentID = <cfqueryparam value="#arguments.documentID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		<cfquery name="local.qryUpdate3" datasource="#application.settings.dsn.trialsmith.dsn#">
			update PurchaseCredits
			set depomemberdataid = <cfqueryparam value="#local.qryGetNewAccount.depomemberdataID#" cfsqltype="CF_SQL_INTEGER">
			where documentID = <cfqueryparam value="#arguments.documentID#" cfsqltype="CF_SQL_INTEGER">
			and depomemberdataID = <cfqueryparam value="#local.qryGetOrigID.depomemberdataID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
	</cfif>
	</cftransaction>
		
	<cfscript>
	local.qryMember = CreateObject("component","models.tsadmin.act_MemberEdit").getMemberBySourceID(local.qryGetNewAccount.sourceID);

	local.data = StructNew();
	local.data.success = true;
	local.data.qryMember = local.qryMember.qryLookup;
	</cfscript>

	<cfreturn local.data>
</cffunction>

<cffunction name="getDocumentSettings" access="public"output="false" returntype="query">
	<cfset var qryDocumentSettings = "">

	<cfquery name="qryDocumentSettings" datasource="#application.settings.dsn.trialsmith.dsn#">
		SELECT depoDocumentSettingID, DepoAmazonBucks, DepoAmazonBucksCredit
		FROM dbo.depoDocumentSettings
	</cfquery>

	<cfreturn qryDocumentSettings>
</cffunction>

<cffunction name="getDocumentsQueueFilter" access="public" output="false" returntype="struct">
	
	<cfset var local = structNew()>

	<cfscript>
	local.filterStruct = {
		contributorFirstName="", contributorLastName="", depoMemberDataID="", expertFirstName="", expertLastName="",
		expertNameContains="", documentID="", style="", orgCode="", depositionDateFrom="", depositionDateTo="", dateEnteredFrom="", dateEnteredTo="", 
		statusSetFrom="", statusSetTo="", documentStatusID="", hasAmazonCredits=0, docHasAttachments='', documentExtension='',
		xodpreapprove='', xodapprove='',excludeDisabledDocs=0, uploadSourceID="", orderBy=5, orderDir="asc"
	};
	// if the struct is not there, add it.
	if (NOT structKeyExists(session,"strDocsQueueFilter")
		OR ListSort(StructKeyList(local.filterStruct),"textnocase") neq ListSort(StructKeyList(session.strDocsQueueFilter),"textnocase")) {
		local.qryDocumentStatuses = getDocumentStatuses();
		local.pendingStatusID = QueryFilter(local.qryDocumentStatuses, function(thisRow) { return arguments.thisRow.statusName EQ 'Pending Approval'; }).statusID;
		local.filterStruct.documentStatusID = local.pendingStatusID;
		saveDocumentsQueueFilter(argumentCollection=local.filterStruct);
	}
	
	return session.strDocsQueueFilter;
	</cfscript>
</cffunction>

<cffunction name="saveDocumentsQueueFilter" access="public" output="false" returntype="void">
	<cfargument name="contributorFirstName" type="string" required="yes">
	<cfargument name="contributorLastName" type="string" required="yes">
	<cfargument name="depoMemberDataID" type="string" required="yes">
	<cfargument name="expertFirstName" type="string" required="yes">
	<cfargument name="expertLastName" type="string" required="yes">
	<cfargument name="expertNameContains" type="string" required="yes">
	<cfargument name="documentID" type="string" required="yes">
	<cfargument name="style" type="string" required="yes">
	<cfargument name="orgCode" type="string" required="yes">
	<cfargument name="depositionDateFrom" type="string" required="yes">
	<cfargument name="depositionDateTo" type="string" required="yes">
	<cfargument name="dateEnteredFrom" type="string" required="yes">
	<cfargument name="dateEnteredTo" type="string" required="yes">
	<cfargument name="statusSetFrom" type="string" required="yes">
	<cfargument name="statusSetTo" type="string" required="yes">
	<cfargument name="documentStatusID" type="string" required="yes">
	<cfargument name="hasAmazonCredits" type="string" required="yes">
	<cfargument name="docHasAttachments" type="string" required="yes">
	<cfargument name="documentExtension" type="string" required="yes">
	<cfargument name="xodpreapprove" type="string" required="yes">
	<cfargument name="xodapprove" type="string" required="yes">
	<cfargument name="excludeDisabledDocs" type="string" required="yes">
	<cfargument name="uploadSourceID" type="string" required="yes">
	<cfargument name="orderBy" type="numeric" required="yes">
	<cfargument name="orderDir" type="string" required="yes">

	<cfset session.strDocsQueueFilter = duplicate(arguments)>
</cffunction>

<cffunction name="getFilteredDocuments" access="public" output="false" returntype="any">
	<cfargument name="contributorFirstName" type="string" required="yes">
	<cfargument name="contributorLastName" type="string" required="yes">
	<cfargument name="depoMemberDataID" type="string" required="yes">
	<cfargument name="expertFirstName" type="string" required="yes">
	<cfargument name="expertLastName" type="string" required="yes">
	<cfargument name="expertNameContains" type="string" required="yes">
	<cfargument name="documentID" type="string" required="yes">
	<cfargument name="style" type="string" required="yes">
	<cfargument name="orgCode" type="string" required="yes">
	<cfargument name="depositionDateFrom" type="string" required="yes">
	<cfargument name="depositionDateTo" type="string" required="yes">
	<cfargument name="dateEnteredFrom" type="string" required="yes">
	<cfargument name="dateEnteredTo" type="string" required="yes">
	<cfargument name="statusSetFrom" type="string" required="yes">
	<cfargument name="statusSetTo" type="string" required="yes">
	<cfargument name="documentStatusID" type="string" required="yes">
	<cfargument name="docHasAttachments" type="string" required="yes">
	<cfargument name="documentExtension" type="string" required="yes">
	<cfargument name="xodpreapprove" type="string" required="yes">
	<cfargument name="xodapprove" type="string" required="yes">
	<cfargument name="uploadSourceID" type="string" required="yes">
	<cfargument name="hasAmazonCredits" type="boolean" required="no" default="0">
	<cfargument name="excludeDisabledDocs" type="string" required="no" default="">
	<cfargument name="orderBy" type="numeric" required="no" default="5">
	<cfargument name="orderDir" type="string" required="no" default="asc">
	<cfargument name="posStart" type="numeric" required="no" default="0">
	<cfargument name="count" type="numeric" required="no" default="100">
	<cfargument name="resultsMode" type="string" required="true">

	<cfset var local = StructNew()>

	<cfif arguments.resultsMode EQ 'list'>
		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"d.documentID")>
		<cfset arrayAppend(local.arrCols,"d.DocumentStatus")>
		<cfset arrayAppend(local.arrCols,"d.ContributorName")>
		<cfset arrayAppend(local.arrCols,"d.ExpertName")>
		<cfset arrayAppend(local.arrCols,"d.DocumentDate")>
		<cfset arrayAppend(local.arrCols,"d.DateEntered")>
		<cfset arrayAppend(local.arrCols,"d.ApprovalDate")>
		<cfset arrayAppend(local.arrCols,"d.PurchaseCreditAmount")>
		<cfset arrayAppend(local.arrCols,"d.DepoAmazonBucksCredit")>
		<cfset local.orderby = local.arrcols[arguments.orderBy] & " #arguments.orderDir#">
	<cfelseif arguments.resultsMode EQ 'export'>
		<cfset local.orderby = "d.DateEntered">
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix="tsadmin")>
	<cfelseif arguments.resultsMode EQ 'bucks'>
		<cfset local.orderby = "d.depoMemberDataID">
	<cfelseif arguments.resultsMode EQ 'buckscsv'>
		<cfset local.orderby = "d.depoMemberDataID">
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix="tsadmin")>
	</cfif>

	<cfquery name="local.qryDocuments" datasource="#application.settings.dsn.trialsmith.dsn#">
		SET XACT_ABORT, NOCOUNT ON;
		BEGIN TRY
			DECLARE @totalCount int;

			IF OBJECT_ID('tempdb..##tmpDocumentsSearch') IS NOT NULL
				DROP TABLE ##tmpDocumentsSearch;
			IF OBJECT_ID('tempdb..##tmpDocuments') IS NOT NULL
				DROP TABLE ##tmpDocuments;

			CREATE TABLE ##tmpDocumentsSearch (documentID int PRIMARY KEY);
			CREATE TABLE ##tmpDocuments (documentID int PRIMARY KEY, DocumentStatus varchar(50), depomemberdataID int, ContributorName varchar(200),
				BillingFirm varchar(200), ContributorEmail varchar(100), ExpertName varchar(500), ExpertFirstName varchar(500), ExpertMiddleName varchar(50), 
				ExpertLastName varchar(500), DocumentDate datetime, XODPreApprove datetime, XODApprove datetime, DateEntered datetime, ApprovalDate datetime, 
				PurchaseCreditAmount decimal(18,2), DepoAmazonBucksCredit decimal(18,2), origHasAttachments bit, originalExt varchar(50), uploadSourceName varchar(25), row INT);

			DECLARE @posStart INT, @posStartAndCount INT;
			SET @posStart = <cfqueryparam value="#arguments.posStart#" cfsqltype="CF_SQL_INTEGER">;
			SET @posStartAndCount = @posStart + <cfqueryparam value="#arguments.count#" cfsqltype="CF_SQL_INTEGER">;

			<cfif listLen(arguments.documentStatusID)>
				DECLARE @tmpDocFilterStatuses TABLE (statusName varchar(25));

				INSERT INTO @tmpDocFilterStatuses (statusName)
				SELECT statusName
				FROM dbo.depoDocumentStatuses
				WHERE statusID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#arguments.documentStatusID#">);
			</cfif>

			INSERT INTO ##tmpDocumentsSearch (documentID)
			SELECT DISTINCT d.DocumentID
			FROM dbo.depoDocuments AS d
			INNER JOIN dbo.depoMemberData AS m ON d.DepomemberdataID = m.depoMemberDataID
			<cfif listLen(arguments.documentStatusID) AND (len(trim(arguments.statusSetFrom)) OR len(trim(arguments.statusSetTo)))>
				INNER JOIN dbo.depoDocumentStatusHistory as sh ON sh.DocumentID = d.DocumentID
					<cfif len(trim(arguments.statusSetFrom))>
						and sh.dateEntered >= <cfqueryparam value="#arguments.statusSetFrom#" cfsqltype="CF_SQL_DATE">
					</cfif>
					<cfif len(trim(arguments.statusSetTo))>
						and sh.dateEntered <= <cfqueryparam value="#arguments.statusSetTo# 23:59:59.997" cfsqltype="CF_SQL_TIMESTAMP">
					</cfif>
				INNER JOIN dbo.depoDocumentStatuses AS ds ON ds.statusID = sh.statusID
				INNER JOIN @tmpDocFilterStatuses as fs on fs.statusName = ds.statusName
			</cfif>
			<cfif listLen(arguments.uploadSourceID)>
				INNER JOIN dbo.depoDocumentUploadSources AS us ON us.uploadSourceID = d.uploadSourceID
					AND us.uploadSourceID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#arguments.uploadSourceID#">)
			</cfif>
			<cfif arguments.xodpreapprove eq 1>
				INNER JOIN dbo.depoDocumentsFilesOnline as dfo on dfo.documentID = d.documentID and dfo.fileType = 'xodpreapprove'
			<cfelseif arguments.xodpreapprove eq 0>
				LEFT OUTER JOIN dbo.depoDocumentsFilesOnline as dfo on dfo.documentID = d.documentID and dfo.fileType = 'xodpreapprove'
			</cfif>
			<cfif arguments.xodapprove eq 1>
				INNER JOIN dbo.depoDocumentsFilesOnline as dfoXF on dfoXF.documentID = d.documentID and dfoXF.fileType = 'xodfull'
			<cfelseif arguments.xodapprove eq 0>
				LEFT OUTER JOIN dbo.depoDocumentsFilesOnline as dfoXF on dfoXF.documentID = d.documentID and dfoXF.fileType = 'xodfull'
			</cfif>
			WHERE 1 = 1
			<cfif len(trim(arguments.contributorFirstName))>
				AND m.FirstName like <cfqueryparam value="%#trim(arguments.contributorFirstName)#%" cfsqltype="CF_SQL_VARCHAR">
			</cfif>
			<cfif len(trim(arguments.contributorLastName))>
				AND m.LastName like <cfqueryparam value="%#trim(arguments.contributorLastName)#%" cfsqltype="CF_SQL_VARCHAR">
			</cfif>
			<cfif len(trim(arguments.depoMemberDataID))>
				AND m.DepoMemberDataID = <cfqueryparam value="#val(arguments.depoMemberDataID)#" cfsqltype="CF_SQL_INTEGER">
			</cfif>
			<cfif len(trim(arguments.expertFirstName))>
				AND d.fname like <cfqueryparam value="%#trim(arguments.expertFirstName)#%" cfsqltype="CF_SQL_VARCHAR">
			</cfif>
			<cfif len(trim(arguments.expertLastName))>
				AND d.lname like <cfqueryparam value="%#trim(arguments.expertLastName)#%" cfsqltype="CF_SQL_VARCHAR">
			</cfif>
			<cfif len(trim(arguments.expertNameContains))>
				AND d.ExpertName like <cfqueryparam value="%#trim(arguments.expertNameContains)#%" cfsqltype="CF_SQL_VARCHAR">
			</cfif>
			<cfif len(trim(arguments.documentID))>
				AND d.DocumentID = <cfqueryparam value="#val(arguments.documentID)#" cfsqltype="CF_SQL_INTEGER">
			</cfif>
			<cfif len(trim(arguments.depositionDateFrom))>
				AND d.DocumentDate >= <cfqueryparam value="#arguments.depositionDateFrom#" cfsqltype="CF_SQL_DATE">
			</cfif>
			<cfif len(trim(arguments.depositionDateTo))>
				AND d.DocumentDate <= <cfqueryparam value="#arguments.depositionDateTo# 23:59:59.997" cfsqltype="CF_SQL_TIMESTAMP">
			</cfif>
			<cfif len(trim(arguments.dateEnteredFrom))>
				AND d.DateEntered >= <cfqueryparam value="#arguments.dateEnteredFrom#" cfsqltype="CF_SQL_DATE">
			</cfif>
			<cfif len(trim(arguments.dateEnteredTo))>
				AND d.DateEntered <= <cfqueryparam value="#arguments.dateEnteredTo# 23:59:59.997" cfsqltype="CF_SQL_TIMESTAMP">
			</cfif>
			<cfif len(trim(arguments.style))>
				AND d.Style like <cfqueryparam value="%#trim(arguments.style)#%" cfsqltype="CF_SQL_VARCHAR">
			</cfif>
			<cfif len(trim(arguments.orgcode))>
				AND d.State = <cfqueryparam value="#trim(arguments.orgcode)#" cfsqltype="CF_SQL_VARCHAR">
			</cfif>
			<cfif arguments.hasAmazonCredits eq 1>
				AND d.DepoAmazonBucks = 1 AND ISNULL(d.DepoAmazonBucksCredit,0) > 0
			</cfif>
			<cfif arguments.excludeDisabledDocs eq 1>
				AND d.disabled <> <cfqueryparam value="Y" cfsqltype="CF_SQL_VARCHAR">
			</cfif>
			<cfif len(arguments.docHasAttachments)>
				AND d.origHasAttachments = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.docHasAttachments#">
			</cfif>
			<cfif len(trim(arguments.documentExtension))>
				AND d.originalExt = <cfqueryparam value="#arguments.documentExtension#" cfsqltype="CF_SQL_VARCHAR">
			</cfif>
			<cfif arguments.xodpreapprove eq 0>
				AND dfo.depoFileID is null
			</cfif>
			<cfif arguments.xodapprove eq 0>
				AND dfoXF.depoFileID is null
			</cfif>;

			WITH allDocuments as (
				SELECT d.DocumentID, ds.statusName AS DocumentStatus,
					d.DepomemberdataID, m.LastName + ', ' + m.FirstName AS ContributorName, m.BillingFirm, m.Email as ContributorEmail,
					d.ExpertName, d.fname as ExpertFirstName, d.mname as ExpertMiddleName, d.lname as ExpertLastName,
					d.DocumentDate, d.DateEntered, UPPER(d.originalExt) as originalExt, 
					CASE WHEN ds.statusName = 'Approved' THEN docSH.dateEntered ELSE NULL END AS ApprovalDate,
					(
						SELECT NULLIF(SUM(PurchaseCreditAmount),0) AS PurchaseCreditAmount
						FROM PurchaseCredits
						WHERE documentID = d.DocumentID
					) AS PurchaseCreditAmount,
					d.DepoAmazonBucksCredit, us.sourceName AS uploadSourceName, d.origHasAttachments, 
					dfo.dateLastModified as xodpreapproveDateLastModified, 
					dfoXF.dateLastModified as xodfullDateLastModified
				FROM dbo.depoDocuments AS d
				INNER JOIN ##tmpDocumentsSearch AS tmp ON tmp.documentID = d.documentID
				INNER JOIN dbo.depoMemberData AS m ON d.DepomemberdataID = m.depomemberdataID
				INNER JOIN dbo.depoDocumentStatusHistory AS docSH ON docSH.depoDocumentHistoryID = d.currentStatusHistoryID
				INNER JOIN dbo.depoDocumentStatuses AS ds ON ds.statusID = docSH.statusID
				LEFT OUTER JOIN dbo.depoDocumentUploadSources AS us ON us.uploadSourceID = d.uploadSourceID
				LEFT OUTER JOIN dbo.depoDocumentsFilesOnline as dfo on dfo.documentID = d.documentID and dfo.fileType = 'xodpreapprove'
				LEFT OUTER JOIN dbo.depoDocumentsFilesOnline as dfoXF on dfoXF.documentID = d.documentID and dfoXF.fileType = 'xodfull'
			)
			INSERT INTO ##tmpDocuments (documentID, DocumentStatus, depomemberdataID, ContributorName, BillingFirm, ContributorEmail, 
				ExpertName, ExpertFirstName, ExpertMiddleName, ExpertLastName, DocumentDate, XODPreApprove, XODApprove,
				DateEntered, ApprovalDate, PurchaseCreditAmount, DepoAmazonBucksCredit, origHasAttachments, originalExt, uploadSourceName, row)
			SELECT d.documentID, d.DocumentStatus, d.depomemberdataID, d.ContributorName, d.BillingFirm, d.ContributorEmail, 
				d.ExpertName, d.ExpertFirstName, d.ExpertMiddleName, d.ExpertLastName,
				d.DocumentDate, d.xodpreapproveDateLastModified, d.xodfullDateLastModified,
				d.DateEntered, d.ApprovalDate, d.PurchaseCreditAmount, d.DepoAmazonBucksCredit, d.origHasAttachments, d.originalExt, 
				d.uploadSourceName, ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)#) AS row
			FROM allDocuments AS d
			<cfif listLen(arguments.documentStatusID)>
				INNER JOIN @tmpDocFilterStatuses AS ds ON ds.statusName = d.DocumentStatus
			</cfif>;

			SELECT @totalCount = @@ROWCOUNT;

			<cfif arguments.resultsMode EQ 'list'>
				SELECT documentID, DocumentStatus, depomemberdataID, ContributorName, BillingFirm, 
					ExpertName, ExpertFirstName, ExpertMiddleName, ExpertLastName,
					DocumentDate, XODPreApprove, XODApprove, DateEntered, ApprovalDate, PurchaseCreditAmount, DepoAmazonBucksCredit, 
					origHasAttachments, originalExt, @totalCount AS totalCount
				FROM ##tmpDocuments
				WHERE row > @posStart
				AND row <= @posStartAndCount
				ORDER BY row;
			<cfelseif arguments.resultsMode EQ 'export'>
				DECLARE @selectsql varchar(max) = '
					SELECT tmp.documentID, tmp.DocumentStatus, tmp.depomemberdataID, tmp.ContributorName, tmp.BillingFirm, tmp.ContributorEmail, 
						tmp.ExpertName, tmp.ExpertFirstName, tmp.ExpertMiddleName, tmp.ExpertLastName, tmp.DocumentDate as [Deposition Date], 
						tmp.XODPreApprove as [PreApproval XOD Updated], tmp.XODApprove as [Approval XOD Updated],
						tmp.DateEntered as [Uploaded Date], tmp.ApprovalDate as [Approved Date], 
						tmp.PurchaseCreditAmount, tmp.DepoAmazonBucksCredit, dd.DepoAmazonBucksFullName, dd.depoAmazonBucksEmail,
						tmp.originalExt as [Orig File Extension], tmp.origHasAttachments AS [PDF Attachments], uploadSourceName AS [Upload Source],
						dd.disabled AS [Disabled], dt.description AS [Document Type],
						ROW_NUMBER() OVER(order by tmp.row) as mcCSVorder 
					*FROM* ##tmpDocuments as tmp
					INNER JOIN trialsmith.dbo.depoDocuments as dd on dd.documentID = tmp.documentID 
					INNER JOIN trialsmith.dbo.depoDocumentTypes dt ON dd.DocumentTypeID = dt.TypeID';
				EXEC membercentral.dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#local.strFolder.folderPathUNC#\Documents.csv', @returnColumns=0;
			<cfelseif arguments.resultsMode EQ 'bucks'>
				declare @totalAmount decimal(18,2);
				SELECT @totalAmount = sum(DepoAmazonBucksCredit) FROM ##tmpDocuments;

				SELECT tmp.depomemberdataID, tmp.ContributorName, tmp.ContributorEmail, ltrim(rtrim(dd.DepoAmazonBucksFullName)) as DepoAmazonBucksFullName, dd.DepoAmazonBucksEmail,
					sum(dd.DepoAmazonBucksCredit) as Total, @totalAmount as overallTotal
				FROM ##tmpDocuments as tmp
				INNER JOIN trialsmith.dbo.depoDocuments as dd on dd.documentID = tmp.documentID
				GROUP BY tmp.depomemberdataID, tmp.ContributorName, tmp.ContributorEmail, ltrim(rtrim(dd.DepoAmazonBucksFullName)), dd.DepoAmazonBucksEmail
				ORDER BY tmp.ContributorName, tmp.depomemberdataID, DepoAmazonBucksFullName;
			<cfelseif arguments.resultsMode EQ 'buckscsv'>
				DECLARE @selectsql varchar(max) = '
					SELECT tmp.depomemberdataID, tmp.ContributorName, tmp.ContributorEmail, ltrim(rtrim(dd.DepoAmazonBucksFullName)) as DepoAmazonBucksFullName, dd.DepoAmazonBucksEmail,
						sum(dd.DepoAmazonBucksCredit) as Total, ROW_NUMBER() OVER(order by tmp.depomemberdataID) as mcCSVorder 
					*FROM* ##tmpDocuments as tmp
					INNER JOIN trialsmith.dbo.depoDocuments as dd on dd.documentID = tmp.documentID
					GROUP BY tmp.depomemberdataID, tmp.ContributorName, tmp.ContributorEmail, ltrim(rtrim(dd.DepoAmazonBucksFullName)), dd.DepoAmazonBucksEmail
					ORDER BY tmp.ContributorName, tmp.depomemberdataID, DepoAmazonBucksFullName';
				EXEC membercentral.dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#local.strFolder.folderPathUNC#\AmazonBucks.csv', @returnColumns=0;
			</cfif>

			IF OBJECT_ID('tempdb..##tmpDocumentsSearch') IS NOT NULL
				DROP TABLE ##tmpDocumentsSearch;
			IF OBJECT_ID('tempdb..##tmpDocuments') IS NOT NULL
				DROP TABLE ##tmpDocuments;
			
		END TRY
		BEGIN CATCH
			IF @@trancount > 0 ROLLBACK TRANSACTION;
			EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
		END CATCH
	</cfquery>

	<cfif listFindNoCase("list,bucks",arguments.resultsMode)>
		<cfreturn local.qryDocuments>
	<cfelseif arguments.resultsMode EQ 'export'>
		<cfreturn { "sourceFilePath":"#local.strFolder.folderPath#/Documents.csv" }>
	<cfelseif arguments.resultsMode EQ 'buckscsv'>
		<cfreturn { "sourceFilePath":"#local.strFolder.folderPath#/AmazonBucks.csv" }>
	</cfif>
</cffunction>

<cffunction name="massDeleteDocuments" access="public" returntype="struct" output="no">
	<cfargument name="documentIDList" type="string" required="yes">

	<cfset var local = StructNew()>

	<cfset local.result = { "success":false }>
	<cftry>
		<cfquery name="local.qryDenyDocuments" datasource="#application.settings.dsn.trialsmith.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				
				IF OBJECT_ID('tempdb..##tmpDocuments') IS NOT NULL 
					DROP TABLE ##tmpDocuments;
				IF OBJECT_ID('tempdb..##tmpNewDocHistoryIDs') IS NOT NULL 
					DROP TABLE ##tmpNewDocHistoryIDs;
				CREATE TABLE ##tmpDocuments (documentID int PRIMARY KEY, depoMemberDataID int, oldStatusID int, purchaseCreditAmountTotal float);
				CREATE TABLE ##tmpNewDocHistoryIDs (depoDocumentHistoryID int, documentID int);

				DECLARE @documentIDList varchar(max), @enteredByDepoMemberDataID int, @statusID int;
				SET @documentIDList = <cfqueryparam value="#arguments.documentIDList#" cfsqltype="CF_SQL_VARCHAR">;
				SET @enteredByDepoMemberDataID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.depomemberDataID#">;

				SELECT @statusID = statusID
				FROM dbo.depoDocumentStatuses
				WHERE statusName = 'Denied';

				-- eligible documents for Deny
				INSERT INTO ##tmpDocuments (documentID, depoMemberDataID, oldStatusID)
				SELECT DISTINCT d.documentID, d.depoMemberDataID, docSH.statusID
				FROM memberCentral.dbo.fn_intListToTable(@documentIDList,',') as tmp
				INNER JOIN dbo.depoDocuments as d on d.documentID = tmp.listitem
					AND ((d.uploadStatus = 1 AND d.reviewFlag = 0) OR d.reviewFlag = 1)
				INNER JOIN dbo.depoDocumentStatusHistory AS docSH ON docSH.depoDocumentHistoryID = d.currentStatusHistoryID;

				UPDATE tmp
				SET tmp.purchaseCreditAmountTotal = (
					SELECT SUM(ISNULL(pc.PurchaseCreditAmount,0))
					FROM PurchaseCredits AS pc
					WHERE pc.DocumentID = tmp.documentID
					AND pc.DepoMemberDataID = tmp.depoMemberDataID
				)
				FROM ##tmpDocuments AS tmp;

				BEGIN TRAN;
					UPDATE d 
					SET uploadStatus = <cfqueryparam value="2" cfsqltype="CF_SQL_INTEGER">,
						reviewFlag = <cfqueryparam value="0" cfsqltype="CF_SQL_INTEGER">,
						disabled = <cfqueryparam value="Y" cfsqltype="CF_SQL_CHAR">
					FROM depoDocuments AS d
					INNER JOIN ##tmpDocuments AS tmp ON tmp.documentID = d.documentID;

					INSERT INTO dbo.depoDocumentStatusHistory (documentID, oldStatusID, statusID, enteredByDepoMemberDataID, dateEntered)
						OUTPUT inserted.depoDocumentHistoryID, inserted.documentID INTO ##tmpNewDocHistoryIDs (depoDocumentHistoryID, documentID)
					SELECT documentID, oldStatusID, @statusID, @enteredByDepoMemberDataID, GETDATE()
					FROM ##tmpDocuments
					WHERE ISNULL(oldStatusID,0) <> @statusID;

					UPDATE d
					SET d.currentStatusHistoryID = tmp.depoDocumentHistoryID
					FROM dbo.depoDocuments AS d
					INNER JOIN ##tmpNewDocHistoryIDs AS tmp ON tmp.documentID = d.documentID;

					-- Reverse purchase credits if any
					INSERT INTO dbo.PurchaseCredits (depoMemberDataID, CreditDate, PurchaseCreditAmount, CreditDescription, documentID)
					SELECT depoMemberDataID, GETDATE(), purchaseCreditAmountTotal * -1, 'Remove credit due to document being disabled', documentID
					FROM ##tmpDocuments
					WHERE ISNULL(purchaseCreditAmountTotal,0) > 0;
				COMMIT TRAN;

				IF OBJECT_ID('tempdb..##tmpDocuments') IS NOT NULL
					DROP TABLE ##tmpDocuments;
				IF OBJECT_ID('tempdb..##tmpNewDocHistoryIDs') IS NOT NULL 
					DROP TABLE ##tmpNewDocHistoryIDs;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
		<cfset result["success"] = true>
	<cfcatch type="Any">
		<cfsilent><cf_tlaexception cfcatch="#cfcatch#"></cfsilent>
		<cfset result["success"] = false>
	</cfcatch>
	</cftry>

	<cfreturn local.result>
</cffunction>

<cffunction name="massChangeCreditType" access="public" returntype="struct" output="no">
	<cfargument name="documentIDList" type="string" required="yes">
	<cfargument name="creditType" type="string" required="yes">
	<cfargument name="purchaseCredit" type="numeric" required="no" default="0">
	<cfargument name="depoAmazonBucksFullName" type="string" required="no" default="">
    <cfargument name="depoAmazonBucksEmail" type="string" required="no" default="">
    <cfargument name="depoAmazonBucksCredit" type="numeric" required="no" default="0">

	<cfset var local = StructNew()>

	<cfset local.result = { "success":false }>
	<cftry>
		<cfquery name="local.qryChangeCreditType" datasource="#application.settings.dsn.trialsmith.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				DECLARE @creditType varchar(50), @documentIDList varchar(max), @depoAmazonBucksCredit decimal(18,2);
				SET @creditType = <cfqueryparam value="#arguments.creditType#" cfsqltype="CF_SQL_VARCHAR">;
				SET @documentIDList = <cfqueryparam value="#arguments.documentIDList#" cfsqltype="CF_SQL_VARCHAR">;

				IF OBJECT_ID('tempdb..##tmpDocuments') IS NOT NULL
					DROP TABLE ##tmpDocuments;

				CREATE TABLE ##tmpDocuments (documentID int PRIMARY KEY, depoMemberDataID int, currentPurchaseCreditAmount decimal(18,2), currentDepoAmazonBucksCredit decimal(18,2));

				INSERT INTO ##tmpDocuments (documentID, depoMemberDataID, currentPurchaseCreditAmount, currentDepoAmazonBucksCredit)
				SELECT DISTINCT d.documentID, d.depoMemberDataID, 0, ISNULL(d.DepoAmazonBucksCredit,0)
				FROM memberCentral.dbo.fn_intListToTable(@documentIDList,',') as tmp
				INNER JOIN dbo.depoDocuments as d on d.documentID = tmp.listitem
					AND d.uploadStatus <> 2
					AND d.disabled = 'N';

				UPDATE tmp
				SET tmp.currentPurchaseCreditAmount = ISNULL(tmp2.PurchaseCreditAmountTotal,0)
				FROM ##tmpDocuments AS tmp
				INNER JOIN (
					SELECT pc.DocumentID, pc.DepoMemberDataID, SUM(ISNULL(pc.PurchaseCreditAmount,0)) AS PurchaseCreditAmountTotal
					FROM PurchaseCredits AS pc
					GROUP BY pc.DocumentID, pc.DepoMemberDataID
				) AS tmp2 ON tmp2.DocumentID = tmp.documentID
					AND tmp2.DepoMemberDataID = tmp.depoMemberDataID;

				BEGIN TRAN;
					INSERT INTO dbo.PurchaseCredits (depoMemberDataID, DocumentID, CreditDate, AmazonBucksCreditAmount, CreditDescription)
					SELECT depoMemberDataID, documentID, getdate(), currentDepoAmazonBucksCredit * -1, 'Remove Amazon Bucks Credits for ressigning (Overwrite Credit Type)'
					FROM ##tmpDocuments
					WHERE currentDepoAmazonBucksCredit <> 0;

					IF @creditType = 'Cash Credits' BEGIN
						INSERT INTO dbo.purchaseCredits (depoMemberDataID, DocumentID, CreditDate, PurchaseCreditAmount, CreditDescription)
						SELECT depoMemberDataID, documentID, getdate(), currentPurchaseCreditAmount * -1, 'Remove credit for ressigning (Overwrite Credit Type)'
						FROM ##tmpDocuments
						WHERE currentPurchaseCreditAmount <> 0;

						INSERT INTO dbo.purchaseCredits (depoMemberDataID, CreditDate, PurchaseCreditAmount, CreditDescription, DocumentID)
						SELECT depoMemberDataID, getdate(), <cfqueryparam value="#arguments.purchaseCredit#" cfsqltype="CF_SQL_DECIMAL">, 'Purchase Credit (Reassigned)', documentID
						FROM ##tmpDocuments;

						UPDATE d
						SET d.DepoAmazonBucksCredit = NULL
						FROM dbo.depoDocuments AS d
						INNER JOIN ##tmpDocuments AS tmp ON tmp.documentID = d.documentID;
					END
					IF @creditType = 'Amazon' BEGIN
						SET @depoAmazonBucksCredit = NULLIF(<cfqueryparam value="#arguments.depoAmazonBucksCredit#" cfsqltype="CF_SQL_DECIMAL">,0);

						INSERT INTO dbo.PurchaseCredits (depoMemberDataID, CreditDate, AmazonBucksCreditAmount, CreditDescription, DocumentID)
						SELECT depoMemberDataID, getdate(), @depoAmazonBucksCredit, 'Amazon Bucks Credits (Reassigned)', documentID
						FROM ##tmpDocuments
						WHERE currentDepoAmazonBucksCredit <> 0;

						UPDATE d
						SET d.DepoAmazonBucks = 1,
							d.DepoAmazonBucksFullName = NULLIF(<cfqueryparam value="#trim(arguments.depoAmazonBucksFullName)#" cfsqltype="CF_SQL_VARCHAR">,''),
							d.DepoAmazonBucksEmail = NULLIF(<cfqueryparam value="#trim(arguments.depoAmazonBucksEmail)#" cfsqltype="CF_SQL_VARCHAR">,''),
							d.DepoAmazonBucksCredit = @depoAmazonBucksCredit
						FROM dbo.depoDocuments AS d
						INNER JOIN ##tmpDocuments AS tmp ON tmp.documentID = d.documentID;
					END
				COMMIT TRAN;

				IF OBJECT_ID('tempdb..##tmpDocuments') IS NOT NULL
					DROP TABLE ##tmpDocuments;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset result["success"] = true>
	<cfcatch type="Any">
		<cfsilent><cf_tlaexception cfcatch="#cfcatch#"></cfsilent>
		<cfset result["success"] = false>
	</cfcatch>
	</cftry>

	<cfreturn local.result>
</cffunction>

<cffunction name="getDocumentStatuses" access="public" output="false" returntype="query">
	<cfset var qryDocStatuses = "">

	<cfquery name="qryDocStatuses" datasource="#application.settings.dsn.trialsmith.dsn#">
		SELECT statusID, statusName
		FROM dbo.depoDocumentStatuses
		ORDER BY statusOrder
	</cfquery>

	<cfreturn qryDocStatuses>
</cffunction>

<cffunction name="getDocumentUploadSources" access="public" output="false" returntype="query">
	<cfset var qryUploadSources = "">

	<cfquery name="qryUploadSources" datasource="#application.settings.dsn.trialsmith.dsn#">
		SELECT uploadSourceID, sourceName
		FROM dbo.depoDocumentUploadSources
		ORDER BY sortOrder
	</cfquery>

	<cfreturn qryUploadSources>
</cffunction>

<cffunction name="getDocumentExtensions" access="public" output="false" returntype="struct">
	<cfset var local = structNew()>
	<cfset local.strData = {}>

	<cfquery name="local.qryDocumentExtensions" datasource="#application.settings.dsn.trialsmith.dsn#">
		SELECT DISTINCT UPPER(originalExt) as originalExt
		FROM dbo.depoDocuments 
		WHERE originalExt <> ''
		ORDER BY 1
	</cfquery>

	<cfset var mainFileExtList = "pdf,doc,docx,ptx,txt">

	<cfset local.strData.qryDocumentExtensionsMain = queryFilter(local.qryDocumentExtensions, function(row){
		return listFindNoCase(mainFileExtList,arguments.row.originalExt);
		} , true)>
	<cfset local.strData.qryDocumentExtensionsOther = queryFilter(local.qryDocumentExtensions, function(row){
		return NOT listFindNoCase(mainFileExtList,arguments.row.originalExt);
		} , true)>

	<cfreturn local.strData>
</cffunction>

<cffunction name="getApprovalDocumentAttachments" access="public" output="false" returntype="array">
	<cfargument name="documentID" type="numeric" required="yes">

	<cfset var local = structNew()>
	<cfset local.arrDocAttachments = []>
	
	<cfset local.s3Bucket = "trialsmith-depos">
	<cfset local.s3region = "us-east-1">
	<cfset local.s3keyMod = numberFormat(arguments.documentID mod 1000,"0000")>
	<cfset local.s3requesttype = "vhost">
	<cfif application.objSiteInfo.isRequestSecure()>
		<cfset local.s3protocol = "https">
	<cfelse>
		<cfset local.s3protocol = "http">
	</cfif>
	<cfset local.s3expire = 30>

	<cfset local.prefixS = lcase("depos/original/#local.s3keyMod#/#arguments.documentID#/")>
	<cfset local.qryDocAttachments = application.objS3.listObjects(bucket=local.s3Bucket, prefix=local.prefixS, delimiter="/", maxResults=0)>

	<cfloop query="local.qryDocAttachments">
		<cfset local.tmpDocStr = { "attachmentName": listFirst(local.qryDocAttachments.name,'.'), "documentS3Link":"", "documentEncPassword":"",
									"attachmentNameBase64Value":"", "attachmentext":listLast(local.qryDocAttachments.name,'.') }>

		<cfset local.tmpDocStr.attachmentNameBase64Value = ToBase64(local.tmpDocStr.attachmentName)>

		<cfset local.arrAmzHeaders = arrayNew(1)>
		<cfset local.tmpStr = { key="response-content-disposition", value="inline;" }>
		<cfset arrayAppend(local.arrAmzHeaders,local.tmpStr)>
		<cfset local.objectKey = lCase("depos/approvals/#local.s3keyMod#/#arguments.documentID#/#local.tmpDocStr.attachmentName#.xod")>
		
		<cfif application.objS3.s3FileExists(bucket=local.s3Bucket, objectKey=local.objectKey, requestType=local.s3requesttype, region=local.s3region)>
			<cfset local.s3Link = application.objS3.s3Url(bucket=local.s3Bucket, objectKey=local.objectKey, requestType=local.s3requesttype, 
				expireInMinutes=local.s3expire, canonicalizedAmzHeaders=local.arrAmzHeaders, region=local.s3region, protocol=local.s3protocol)>

			<cfset local.tmpDocStr["documentS3Link"] = local.s3Link>
			<cfset local.unencryptedPassword = "tsdocumentID#local.tmpDocStr.attachmentName#/#local.s3keyMod#/#local.tmpDocStr.attachmentName#">
			<cfset local.tmpDocStr["documentEncPassword"] = lcase(hash(local.unencryptedPassword))>
		</cfif>

		<cfset arrayAppend(local.arrDocAttachments, local.tmpDocStr)>
	</cfloop>

	<cfreturn local.arrDocAttachments>
</cffunction>

<cffunction name="processOriginalPDF" access="public" output="false" returntype="void">
	<cfargument name="documentID" type="numeric" required="yes">

	<cfset var local = structNew()>
	
	<!--- immediately mark as not having attachments --->
	<cfquery name="local.qryUpdateDepoDocument" datasource="#application.settings.dsn.trialsmith.dsn#">
		UPDATE dbo.depoDocuments
		SET origHasAttachments = 0, origCheckedForAttachments = 0
		WHERE documentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.documentID#">
	</cfquery>

	<!--- feed queue to reprocess --->
	<cfstoredproc procedure="ts_addDepoDocumentToPreApproveQueue" datasource="#application.settings.dsn.trialsmith.dsn#">
		<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.documentID#">
	</cfstoredproc>
</cffunction>

<cffunction name="processApprovedPDF" access="public" output="false" returntype="void">
	<cfargument name="documentID" type="numeric" required="yes">

	<cfset var local = structNew()>
	<cfset local.qryDocument = getDocument(arguments.documentID)>

	<cfif (local.qryDocument.documentStatusName EQ 'Approved' or local.qryDocument.documentStatusName EQ 'Flagged for Review') AND application.settings.environment eq "production">
		<cfset local.pdfFileName = "#local.qryDocument.documentid#.pdf">
		<cfset local.s3keyMod = numberFormat(local.qryDocument.documentid mod 1000,"0000")>
		<cfset local.objectKey = lcase("depos/documenttoprocess/#local.s3keyMod#/#local.pdfFileName#")>
		
		<cfif application.objS3.s3FileExists(bucket='trialsmith-depos', objectKey=local.objectKey, requestType="vhost")>
			<cfset local.arrAmzHeaders = arrayNew(1)>
			<cfset local.tmpStr = { key="response-content-disposition", value="attachment; filename=""#local.pdfFileName#""; filename*=UTF-8''#urlEncodedFormat(local.pdfFileName)#" }>
			<cfset arrayAppend(local.arrAmzHeaders,local.tmpStr)>

			<cfset local.s3FileURL = application.objS3.s3Url(bucket='trialsmith-depos', objectKey=local.objectKey, requestType="vhost", canonicalizedAmzHeaders=local.arrAmzHeaders)>
			<cfif len(local.s3FileURL)>
				<!--- download pdf to local folder --->
				<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix="tsadmin")>
				<cfhttp method="get" getasbinary="yes" url="#local.s3FileURL#" path="#local.strFolder.folderPath#" file="#local.pdfFileName#" timeout="60" throwonerror="true"></cfhttp>
				
				<!--- delete xod files if exists --->
				<cfset local.xodFileName = "#local.qryDocument.documentid#.xod">
				<cfset local.xodFileObjectKey = lcase("depos/xod/#local.s3keyMod#/#local.xodFileName#")>
				<cfif application.objS3.s3FileExists(bucket='trialsmith-depos', objectKey=local.xodFileObjectKey, requestType="vhost")>
					<cfset application.objAWS_SQS.s3.deleteObject(Bucket="trialsmith-depos", objectKey=local.xodFileObjectKey)>
				</cfif>

				<cfset local.xodPreviewFileObjectKey = lcase("depos/xodpreview/#local.s3keyMod#/#local.xodFileName#")>
				<cfif application.objS3.s3FileExists(bucket='trialsmith-depos', objectKey=local.xodPreviewFileObjectKey, requestType="vhost")>
					<cfset application.objAWS_SQS.s3.deleteObject(Bucket="trialsmith-depos", objectKey=local.xodPreviewFileObjectKey)>
				</cfif>

				<!--- reupload the file --->
				<cfset application.objAWS_SQS.s3.putObject(Bucket="trialsmith-depos", objectKey=local.objectKey, FileContent=fileReadBinary("#local.strFolder.folderPath#/#local.pdfFileName#"))>
			</cfif>
		</cfif>
	</cfif>	
</cffunction>

<cffunction name="reuploadOriginalDocument" access="public" hint="reuploads the original document." returntype="struct" output="no">
	<cfargument name="formfields" type="struct" required="yes">
	
	<cfset var local = structNew()>
	<cfset local.uploadResult = { "success":true }>
	<cfset local.settingsUploadPath = application.settings.DOCS_UPLOADS_PATH>
	
	<!--- Attempt upload of document --->
	<cftry>
		<cffile action="upload" filefield="#arguments.formfields.newDocFile#" destination="#local.settingsUploadPath#" result="local.strUploadResult" nameconflict="makeunique">

		<cfif local.strUploadResult.fileWasSaved>
			<cfset local.fileName = "#request.documentID#.#lcase(local.strUploadResult.serverFileExt)#">
			<cfset local.originalDocUploadFilePath = "#application.settings.DOCS_ORIGINALS_PATH##local.fileName#">
			<cfset local.originalDocS3UploadFilePath = "#application.settings.DOCS_ORIGINALS_S3UPLOAD_PATH##local.fileName#">

			<!--- rename file to documentID --->
			<cffile action="RENAME" source="#local.settingsUploadPath##local.strUploadResult.serverfile#" destination="#local.settingsUploadPath##local.fileName#">
			<!--- copy file to original docs --->
			<cffile action="COPY" source="#local.settingsUploadPath##local.fileName#" destination="#local.originalDocUploadFilePath#">

			<cfset local.s3keyMod = numberFormat(request.documentID mod 1000,"0000")>
			<cfset local.s3objectKey = lcase("depos/reprocessed/#local.s3keyMod#/#local.fileName#")>
			<cfset addToS3UploadQueue(filepath=local.originalDocS3UploadFilePath, objectKey=local.s3objectKey)>
		</cfif>
	<cfcatch type="Any">
		<cfsilent><cf_tlaexception cfcatch="#cfcatch#"></cfsilent>
		<!--- if any errors, delete the uploaded file --->
		<cfif StructKeyExists(local.strUploadResult, "serverfile") AND FileExists("#local.settingsUploadPath##local.strUploadResult.serverfile#")>
			<cffile action="DELETE" file="#local.settingsUploadPath##local.strUploadResult.serverfile#">
		</cfif>
		<cfif StructKeyExists(local.strUploadResult, "serverfile") AND FileExists("#local.settingsUploadPath##request.documentID#.#lcase(local.strUploadResult.serverFileExt)#")>
			<cffile action="DELETE" file="#local.settingsUploadPath##request.documentID#.#lcase(local.strUploadResult.serverFileExt)#">
		</cfif>
		<cfset local.uploadResult["success"] = false>
	</cfcatch>
	</cftry>
	
	<cfreturn local.uploadResult>
</cffunction>

</cfcomponent>